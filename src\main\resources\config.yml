# Configuration du plugin CivilizationMc
# Version: 1.0.0

# Limites du plugin
limits:
  # Nombre maximum de chunks qu'une civilisation peut revendiquer
  max-claims-per-civilization: 50
  # Nombre maximum de membres par civilisation
  max-members-per-civilization: 20

# Paramètres des civilisations
civilization:
  # Longueur minimale du nom d'une civilisation
  min-name-length: 3
  # Longueur maximale du nom d'une civilisation
  max-name-length: 16

# Paramètres de gameplay
gameplay:
  # Autoriser le PvP dans les chunks revendiqués
  allow-pvp-in-claimed-chunks: false
  # Autoriser l'abandon de chunks
  allow-chunk-unclaim: true

# Paramètres du scoreboard
scoreboard:
  # Activer l'affichage du scoreboard lors de l'entrée dans un chunk revendiqué
  enabled: true
  # Afficher les relations diplomatiques dans le scoreboard
  show-relations: true
  # Afficher la date de création de la civilisation
  show-creation-date: true
  # Afficher l'état du chat privé
  show-chat-status: true

# Mondes activés (vide = tous les mondes)
enabled-worlds: []

# Mondes désactivés
disabled-worlds:
  - "world_nether"
  - "world_the_end"

# Stockage des données
storage:
  # Les données sont maintenant stockées dans des fichiers YAML
  # civilizations.yml - Données des civilisations
  # chunks.yml - Chunks revendiqués
  # players.yml - Associations joueur-civilisation
  # Configuration MySQL (si type = mysql)
  mysql:
    host: "localhost"
    port: 3306
    database: "civilizationmc"
    username: "root"
    password: ""

# Messages personnalisables
messages:
  civilization-created: "&aVotre civilisation &6{name} &aa été créée avec succès!"
  civilization-already-exists: "&cUne civilisation avec ce nom existe déjà!"
  player-already-in-civilization: "&cVous êtes déjà membre d'une civilisation!"
  player-not-in-civilization: "&cVous n'êtes membre d'aucune civilisation!"
  not-civilization-owner: "&cSeul le propriétaire de la civilisation peut faire cela!"
  chunk-claimed: "&aChunk revendiqué avec succès!"
  chunk-already-claimed: "&cCe chunk est déjà revendiqué par &6{civilization}&c!"
  chunk-unclaimed: "&aChunk abandonné avec succès!"
  chunk-not-claimed: "&cCe chunk n'est pas revendiqué!"
  max-claims-reached: "&cVotre civilisation a atteint le nombre maximum de chunks revendiqués!"
  home-set: "&aHome défini avec succès!"
  home-teleport: "&aTéléportation au home..."
  no-home-set: "&cAucun home n'est défini pour votre civilisation!"
  player-invited: "&a{player} a été invité dans votre civilisation!"
  invitation-sent: "&aInvitation envoyée à &6{player}&a!"
  invitation-received: "&aVous avez été invité à rejoindre la civilisation &6{civilization}&a!"
  player-joined: "&a{player} a rejoint votre civilisation!"
  player-kicked: "&a{player} a été exclu de la civilisation!"
  chat-enabled: "&aChat privé de civilisation activé!"
  chat-disabled: "&aChat privé de civilisation désactivé!"
  war-declared: "&cGuerre déclarée à &6{civilization}&c!"
  peace-proposed: "&aProposition de paix envoyée à &6{civilization}&a!"
  alliance-proposed: "&aProposition d'alliance envoyée à &6{civilization}&a!"
  relation-updated: "&aRelation avec &6{civilization} &amise à jour: &6{relation}"
