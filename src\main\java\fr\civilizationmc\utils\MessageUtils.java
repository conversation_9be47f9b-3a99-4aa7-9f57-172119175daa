package fr.civilizationmc.utils;

import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.Set;
import java.util.UUID;

public class MessageUtils {
    
    public static final String PREFIX = ChatColor.GOLD + "[CivilizationMc] " + ChatColor.RESET;
    
    // Couleurs pour différents types de messages
    public static final ChatColor SUCCESS = ChatColor.GREEN;
    public static final ChatColor ERROR = ChatColor.RED;
    public static final ChatColor WARNING = ChatColor.YELLOW;
    public static final ChatColor INFO = ChatColor.AQUA;
    public static final ChatColor HIGHLIGHT = ChatColor.GOLD;
    
    /**
     * Envoie un message simple avec le préfixe du plugin
     */
    public static void sendMessage(CommandSender sender, String message) {
        sender.sendMessage(PREFIX + message);
    }
    
    /**
     * Envoie un message d'information (bleu)
     */
    public static void sendInfo(CommandSender sender, String message) {
        sender.sendMessage(PREFIX + INFO + message);
    }
    
    /**
     * Envoie un message de succès (vert)
     */
    public static void sendSuccess(CommandSender sender, String message) {
        sender.sendMessage(PREFIX + SUCCESS + message);
    }
    
    /**
     * Envoie un message d'avertissement (jaune)
     */
    public static void sendWarning(CommandSender sender, String message) {
        sender.sendMessage(PREFIX + WARNING + message);
    }
    
    /**
     * Envoie un message d'erreur (rouge)
     */
    public static void sendError(CommandSender sender, String message) {
        sender.sendMessage(PREFIX + ERROR + message);
    }
    
    /**
     * Envoie un message sans préfixe
     */
    public static void sendRaw(CommandSender sender, String message) {
        sender.sendMessage(message);
    }
    
    /**
     * Envoie un message coloré personnalisé
     */
    public static void sendColored(CommandSender sender, String message, ChatColor color) {
        sender.sendMessage(PREFIX + color + message);
    }
    
    /**
     * Formate un message avec des couleurs
     */
    public static String colorize(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    /**
     * Supprime toutes les couleurs d'un message
     */
    public static String stripColors(String message) {
        return ChatColor.stripColor(message);
    }
    
    /**
     * Envoie un message à tous les membres d'une civilisation
     */
    public static void broadcastToCivilization(Set<UUID> members, String message) {
        for (UUID memberId : members) {
            Player player = org.bukkit.Bukkit.getPlayer(memberId);
            if (player != null && player.isOnline()) {
                sendMessage(player, message);
            }
        }
    }
    
    /**
     * Envoie un message de chat privé de civilisation
     */
    public static void sendCivilizationChat(Set<UUID> members, String civilizationName, String playerName, String message) {
        String formattedMessage = ChatColor.DARK_GREEN + "[" + civilizationName + "] " + 
                                ChatColor.GREEN + playerName + ChatColor.WHITE + ": " + message;
        
        for (UUID memberId : members) {
            Player player = org.bukkit.Bukkit.getPlayer(memberId);
            if (player != null && player.isOnline()) {
                player.sendMessage(formattedMessage);
            }
        }
    }
    
    /**
     * Envoie un message à tous les membres d'une civilisation
     */
    public static void sendCivilizationMessage(Set<UUID> members, String message) {
        String formattedMessage = PREFIX + ChatColor.AQUA + message;
        
        for (UUID memberUuid : members) {
            Player member = org.bukkit.Bukkit.getPlayer(memberUuid);
            if (member != null && member.isOnline()) {
                member.sendMessage(formattedMessage);
            }
        }
    }
    
    /**
     * Formate le nom d'un joueur avec des couleurs
     */
    public static String formatPlayerName(String playerName) {
        return ChatColor.YELLOW + playerName + ChatColor.RESET;
    }
    
    /**
     * Formate le nom d'une civilisation avec des couleurs
     */
    public static String formatCivilizationName(String civilizationName) {
        return ChatColor.GOLD.toString() + ChatColor.BOLD.toString() + civilizationName + ChatColor.RESET.toString();
    }
}
