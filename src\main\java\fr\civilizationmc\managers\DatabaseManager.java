package fr.civilizationmc.managers;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Civilization;
import fr.civilizationmc.models.ClaimedChunk;
import fr.civilizationmc.models.RelationType;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class DatabaseManager {

    private final CivilizationMc plugin;
    private final Map<String, Civilization> civilizations;
    private final Map<String, ClaimedChunk> claimedChunks;
    private final Map<UUID, String> playerCivilizations;

    private SQLiteManager sqliteManager;
    private YamlMigrator yamlMigrator;

    public DatabaseManager(CivilizationMc plugin) {
        this.plugin = plugin;
        this.civilizations = new ConcurrentHashMap<>();
        this.claimedChunks = new ConcurrentHashMap<>();
        this.playerCivilizations = new ConcurrentHashMap<>();

        // Initialisation SQLite
        this.sqliteManager = new SQLiteManager(plugin);
        this.yamlMigrator = new YamlMigrator(plugin, sqliteManager);

        initialize();
    }
    
    private void initialize() {
        // Initialiser SQLite
        sqliteManager.initialize();

        // Vérifier s'il faut migrer depuis YAML
        if (yamlMigrator.needsMigration()) {
            plugin.getLogger().info("Migration des données YAML vers SQLite...");
            yamlMigrator.migrate();
        }

        // Charger les données depuis SQLite
        loadData();
        plugin.getLogger().info("Données chargées depuis SQLite!");
    }

    private void loadData() {
        loadCivilizations();
        loadChunks();
        loadPlayers();
    }
    
    private void loadCivilizations() {
        try (ResultSet rs = sqliteManager.executeQuery("SELECT * FROM civilizations")) {
            if (rs == null) return;

            while (rs.next()) {
                String name = rs.getString("name");
                UUID ownerId = UUID.fromString(rs.getString("owner_id"));
                String ownerName = rs.getString("owner_name");

                Civilization civilization = new Civilization(name, ownerId, ownerName);

                // Charger les propriétés de base
                civilization.setChatEnabled(rs.getBoolean("chat_enabled"));
                civilization.setCreationDate(rs.getLong("creation_date"));

                // Charger le home si présent
                String homeWorld = rs.getString("home_world");
                if (homeWorld != null) {
                    double x = rs.getDouble("home_x");
                    double y = rs.getDouble("home_y");
                    double z = rs.getDouble("home_z");
                    float yaw = rs.getFloat("home_yaw");
                    float pitch = rs.getFloat("home_pitch");

                    Location home = new Location(Bukkit.getWorld(homeWorld), x, y, z, yaw, pitch);
                    civilization.setHomeLocation(home);
                }

                // Charger les membres
                loadCivilizationMembers(civilization);

                // Charger les chunks
                loadCivilizationChunks(civilization);

                // Charger les relations diplomatiques
                loadCivilizationRelations(civilization);

                civilizations.put(name, civilization);
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur lors du chargement des civilisations : " + e.getMessage());
        }
    }

    private void loadCivilizationMembers(Civilization civilization) {
        try (ResultSet rs = sqliteManager.executeQuery(
                "SELECT player_id FROM civilization_members WHERE civilization_name = ?",
                civilization.getName())) {
            if (rs == null) return;

            while (rs.next()) {
                UUID playerId = UUID.fromString(rs.getString("player_id"));
                civilization.addMember(playerId);
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur lors du chargement des membres de " + civilization.getName() + " : " + e.getMessage());
        }
    }

    private void loadCivilizationChunks(Civilization civilization) {
        try (ResultSet rs = sqliteManager.executeQuery(
                "SELECT world, x, z FROM claimed_chunks WHERE civilization_name = ?",
                civilization.getName())) {
            if (rs == null) return;

            while (rs.next()) {
                String world = rs.getString("world");
                int x = rs.getInt("x");
                int z = rs.getInt("z");
                String chunkKey = world + "_" + x + "_" + z;
                civilization.addClaimedChunk(chunkKey);
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur lors du chargement des chunks de " + civilization.getName() + " : " + e.getMessage());
        }
    }

    private void loadCivilizationRelations(Civilization civilization) {
        try (ResultSet rs = sqliteManager.executeQuery(
                "SELECT target_civilization, relation_type FROM diplomatic_relations WHERE civilization_name = ?",
                civilization.getName())) {
            if (rs == null) return;

            while (rs.next()) {
                String targetCiv = rs.getString("target_civilization");
                String relationStr = rs.getString("relation_type");
                RelationType relation = RelationType.valueOf(relationStr);
                civilization.setRelation(targetCiv, relation);
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur lors du chargement des relations de " + civilization.getName() + " : " + e.getMessage());
        }
    }

    private void loadChunks() {
        try (ResultSet rs = sqliteManager.executeQuery("SELECT * FROM claimed_chunks")) {
            if (rs == null) return;

            while (rs.next()) {
                String world = rs.getString("world");
                int x = rs.getInt("x");
                int z = rs.getInt("z");
                String civilizationName = rs.getString("civilization_name");
                long claimDate = rs.getLong("claim_date");

                String chunkKey = world + "_" + x + "_" + z;
                ClaimedChunk chunk = new ClaimedChunk(world, x, z, civilizationName, null);
                claimedChunks.put(chunkKey, chunk);
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur lors du chargement des chunks : " + e.getMessage());
        }
    }
    
    private void loadPlayers() {
        try (ResultSet rs = sqliteManager.executeQuery("SELECT * FROM players")) {
            if (rs == null) return;

            while (rs.next()) {
                String playerIdStr = rs.getString("player_id");
                String civilizationName = rs.getString("civilization_name");

                if (playerIdStr != null && civilizationName != null) {
                    UUID playerId = UUID.fromString(playerIdStr);
                    playerCivilizations.put(playerId, civilizationName);
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur lors du chargement des joueurs : " + e.getMessage());
        }
    }

    public void saveAll() {
        // Avec SQLite, les données sont sauvegardées automatiquement lors des opérations
        // Cette méthode est conservée pour la compatibilité
        plugin.getLogger().info("Données SQLite synchronisées!");
    }
    
    public void close() {
        if (sqliteManager != null) {
            sqliteManager.close();
        }
        plugin.getLogger().info("Base de données SQLite fermée!");
    }
    
    // Méthodes pour les civilisations
    public void saveCivilization(Civilization civilization) {
        civilizations.put(civilization.getName(), civilization);

        // Sauvegarder dans SQLite
        String sql = """
            INSERT OR REPLACE INTO civilizations
            (name, owner_id, owner_name, chat_enabled, creation_date, home_world, home_x, home_y, home_z, home_yaw, home_pitch)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """;

        Location home = civilization.getHomeLocation();
        sqliteManager.executeUpdate(sql,
            civilization.getName(),
            civilization.getOwnerId().toString(),
            civilization.getOwnerName(),
            civilization.isChatEnabled() ? 1 : 0,
            civilization.getCreationDate(),
            home != null ? home.getWorld().getName() : null,
            home != null ? home.getX() : null,
            home != null ? home.getY() : null,
            home != null ? home.getZ() : null,
            home != null ? home.getYaw() : null,
            home != null ? home.getPitch() : null
        );

        // Sauvegarder les membres
        saveCivilizationMembers(civilization);

        // Sauvegarder les relations
        saveCivilizationRelations(civilization);
    }

    private void saveCivilizationMembers(Civilization civilization) {
        // Supprimer les anciens membres
        sqliteManager.executeUpdate("DELETE FROM civilization_members WHERE civilization_name = ?", civilization.getName());

        // Ajouter les nouveaux membres
        for (UUID member : civilization.getMembers()) {
            sqliteManager.executeUpdate("INSERT INTO civilization_members (civilization_name, player_id) VALUES (?, ?)",
                civilization.getName(), member.toString());
        }
    }

    private void saveCivilizationRelations(Civilization civilization) {
        // Supprimer les anciennes relations
        sqliteManager.executeUpdate("DELETE FROM diplomatic_relations WHERE civilization_name = ?", civilization.getName());

        // Ajouter les nouvelles relations
        for (Map.Entry<String, RelationType> entry : civilization.getRelations().entrySet()) {
            sqliteManager.executeUpdate("INSERT INTO diplomatic_relations (civilization_name, target_civilization, relation_type) VALUES (?, ?, ?)",
                civilization.getName(), entry.getKey(), entry.getValue().name());
        }
    }

    public Civilization getCivilization(String name) {
        return civilizations.get(name);
    }
    
    public Collection<Civilization> getAllCivilizations() {
        return civilizations.values();
    }
    

    
    // Méthodes pour les chunks
    public void saveClaimedChunk(ClaimedChunk chunk) {
        String key = chunk.getWorldName() + "_" + chunk.getChunkX() + "_" + chunk.getChunkZ();
        claimedChunks.put(key, chunk);

        // Sauvegarder dans SQLite
        String sql = "INSERT OR REPLACE INTO claimed_chunks (world, x, z, civilization_name, claim_date) VALUES (?, ?, ?, ?, ?)";
        sqliteManager.executeUpdate(sql,
            chunk.getWorldName(),
            chunk.getChunkX(),
            chunk.getChunkZ(),
            chunk.getCivilizationName(),
            chunk.getClaimDate()
        );
    }

    public ClaimedChunk getClaimedChunk(String world, int x, int z) {
        String key = world + "_" + x + "_" + z;
        return claimedChunks.get(key);
    }

    public void removeClaimedChunk(String world, int x, int z) {
        String key = world + "_" + x + "_" + z;
        claimedChunks.remove(key);

        // Supprimer de SQLite
        String sql = "DELETE FROM claimed_chunks WHERE world = ? AND x = ? AND z = ?";
        sqliteManager.executeUpdate(sql, world, x, z);
    }
    
    public Collection<ClaimedChunk> getAllClaimedChunks() {
        return claimedChunks.values();
    }
    
    // Méthodes pour les joueurs
    public void setPlayerCivilization(UUID playerId, String civilizationName) {
        if (civilizationName == null) {
            playerCivilizations.remove(playerId);
            // Supprimer de SQLite
            sqliteManager.executeUpdate("DELETE FROM players WHERE player_id = ?", playerId.toString());
        } else {
            playerCivilizations.put(playerId, civilizationName);
            // Sauvegarder dans SQLite
            sqliteManager.executeUpdate("INSERT OR REPLACE INTO players (player_id, civilization_name) VALUES (?, ?)",
                playerId.toString(), civilizationName);
        }
    }
    
    public String getPlayerCivilization(UUID playerId) {
        return playerCivilizations.get(playerId);
    }
    
    public Map<UUID, String> getAllPlayerCivilizations() {
        return new HashMap<>(playerCivilizations);
    }
}
