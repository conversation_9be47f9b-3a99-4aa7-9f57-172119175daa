package fr.civilizationmc.managers;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Civilization;
import fr.civilizationmc.models.ClaimedChunk;
import fr.civilizationmc.models.RelationType;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Gestionnaire principal de la base de données
 * Utilise des fichiers YAML pour la persistance des données
 */
public class DatabaseManager {

    private final CivilizationMc plugin;
    private final File dataFolder;

    // Cache en mémoire pour les performances
    private final Map<String, Civilization> civilizations;
    private final Map<String, ClaimedChunk> claimedChunks;
    private final Map<UUID, String> playerCivilizations;

    // Fichiers YAML
    private File civilizationsFile;
    private File chunksFile;
    private File playersFile;

    public DatabaseManager(CivilizationMc plugin) {
        this.plugin = plugin;
        this.dataFolder = plugin.getDataFolder();
        this.civilizations = new ConcurrentHashMap<>();
        this.claimedChunks = new ConcurrentHashMap<>();
        this.playerCivilizations = new ConcurrentHashMap<>();

        // Créer le dossier de données s'il n'existe pas
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }

        // Initialiser les fichiers YAML
        this.civilizationsFile = new File(dataFolder, "civilizations.yml");
        this.chunksFile = new File(dataFolder, "chunks.yml");
        this.playersFile = new File(dataFolder, "players.yml");

        initialize();
    }

    /**
     * Initialise le gestionnaire de base de données YAML
     */
    private void initialize() {
        plugin.getLogger().info("Initialisation du gestionnaire de base de données YAML...");

        try {
            loadData();
            plugin.getLogger().info("Données chargées depuis les fichiers YAML!");
        } catch (Exception e) {
            plugin.getLogger().severe("Erreur lors de l'initialisation de la base de données : " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Charge toutes les données depuis les fichiers YAML
     */
    private void loadData() {
        loadCivilizations();
        loadChunks();
        loadPlayers();
    }

    /**
     * Charge les civilisations depuis le fichier YAML
     */
    private void loadCivilizations() {
        if (!civilizationsFile.exists()) {
            plugin.getLogger().info("Fichier civilizations.yml non trouvé, création d'un nouveau fichier.");
            return;
        }

        try {
            FileConfiguration config = YamlConfiguration.loadConfiguration(civilizationsFile);
            ConfigurationSection civilizationsSection = config.getConfigurationSection("civilizations");

            if (civilizationsSection == null) {
                return;
            }

            for (String name : civilizationsSection.getKeys(false)) {
                ConfigurationSection civSection = civilizationsSection.getConfigurationSection(name);
                if (civSection == null) continue;

                try {
                    UUID ownerId = UUID.fromString(civSection.getString("owner-id"));
                    String ownerName = civSection.getString("owner-name");

                    Civilization civilization = new Civilization(name, ownerId, ownerName);

                    // Charger les propriétés de base
                    civilization.setChatEnabled(civSection.getBoolean("chat-enabled", false));
                    civilization.setCreationDate(civSection.getLong("creation-date", System.currentTimeMillis()));

                    // Charger le home si présent
                    if (civSection.contains("home")) {
                        ConfigurationSection homeSection = civSection.getConfigurationSection("home");
                        if (homeSection != null) {
                            String worldName = homeSection.getString("world");
                            if (worldName != null && Bukkit.getWorld(worldName) != null) {
                                double x = homeSection.getDouble("x");
                                double y = homeSection.getDouble("y");
                                double z = homeSection.getDouble("z");
                                float yaw = (float) homeSection.getDouble("yaw", 0.0);
                                float pitch = (float) homeSection.getDouble("pitch", 0.0);

                                Location home = new Location(Bukkit.getWorld(worldName), x, y, z, yaw, pitch);
                                civilization.setHomeLocation(home);
                            }
                        }
                    }

                    // Charger les membres
                    List<String> membersList = civSection.getStringList("members");
                    for (String memberStr : membersList) {
                        try {
                            UUID memberId = UUID.fromString(memberStr);
                            civilization.addMember(memberId);
                        } catch (IllegalArgumentException e) {
                            plugin.getLogger().warning("UUID invalide pour un membre de " + name + " : " + memberStr);
                        }
                    }

                    // Charger les chunks revendiqués
                    List<String> chunksList = civSection.getStringList("claimed-chunks");
                    for (String chunkKey : chunksList) {
                        civilization.addClaimedChunk(chunkKey);
                    }

                    // Charger les relations diplomatiques
                    ConfigurationSection relationsSection = civSection.getConfigurationSection("relations");
                    if (relationsSection != null) {
                        for (String targetCiv : relationsSection.getKeys(false)) {
                            try {
                                String relationStr = relationsSection.getString(targetCiv);
                                RelationType relation = RelationType.valueOf(relationStr);
                                civilization.setRelation(targetCiv, relation);
                            } catch (IllegalArgumentException e) {
                                plugin.getLogger().warning("Type de relation invalide pour " + name + " -> " + targetCiv);
                            }
                        }
                    }

                    civilizations.put(name, civilization);
                    plugin.getLogger().info("Civilisation chargée : " + name);

                } catch (Exception e) {
                    plugin.getLogger().warning("Erreur lors du chargement de la civilisation " + name + " : " + e.getMessage());
                }
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Erreur lors du chargement des civilisations : " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Charge les chunks revendiqués depuis le fichier YAML
     */
    private void loadChunks() {
        if (!chunksFile.exists()) {
            plugin.getLogger().info("Fichier chunks.yml non trouvé, création d'un nouveau fichier.");
            return;
        }

        try {
            FileConfiguration config = YamlConfiguration.loadConfiguration(chunksFile);
            ConfigurationSection chunksSection = config.getConfigurationSection("chunks");

            if (chunksSection == null) {
                return;
            }

            for (String chunkKey : chunksSection.getKeys(false)) {
                ConfigurationSection chunkSection = chunksSection.getConfigurationSection(chunkKey);
                if (chunkSection == null) continue;

                try {
                    String[] parts = chunkKey.split("_");
                    if (parts.length != 3) continue;

                    String world = parts[0];
                    int x = Integer.parseInt(parts[1]);
                    int z = Integer.parseInt(parts[2]);
                    String civilizationName = chunkSection.getString("civilization");
                    long claimDate = chunkSection.getLong("claim-date", System.currentTimeMillis());

                    ClaimedChunk chunk = new ClaimedChunk(world, x, z, civilizationName, null);
                    claimedChunks.put(chunkKey, chunk);

                } catch (Exception e) {
                    plugin.getLogger().warning("Erreur lors du chargement du chunk " + chunkKey + " : " + e.getMessage());
                }
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Erreur lors du chargement des chunks : " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Charge les associations joueur-civilisation depuis le fichier YAML
     */
    private void loadPlayers() {
        if (!playersFile.exists()) {
            plugin.getLogger().info("Fichier players.yml non trouvé, création d'un nouveau fichier.");
            return;
        }

        try {
            FileConfiguration config = YamlConfiguration.loadConfiguration(playersFile);
            ConfigurationSection playersSection = config.getConfigurationSection("players");

            if (playersSection == null) {
                return;
            }

            for (String playerIdStr : playersSection.getKeys(false)) {
                try {
                    UUID playerId = UUID.fromString(playerIdStr);
                    String civilizationName = playersSection.getString(playerIdStr + ".civilization");

                    if (civilizationName != null) {
                        playerCivilizations.put(playerId, civilizationName);
                    }
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("UUID invalide pour un joueur : " + playerIdStr);
                }
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Erreur lors du chargement des joueurs : " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Sauvegarde toutes les données dans les fichiers YAML
     */
    public void saveAll() {
        try {
            saveCivilizationsToFile();
            saveChunksToFile();
            savePlayersToFile();
            plugin.getLogger().info("Toutes les données ont été sauvegardées dans les fichiers YAML!");
        } catch (Exception e) {
            plugin.getLogger().severe("Erreur lors de la sauvegarde : " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Ferme le gestionnaire de base de données
     */
    public void close() {
        saveAll();
        plugin.getLogger().info("Gestionnaire de base de données YAML fermé!");
    }

    // ========== MÉTHODES POUR LES CIVILISATIONS ==========

    /**
     * Sauvegarde une civilisation en mémoire et dans le fichier YAML
     */
    public void saveCivilization(Civilization civilization) {
        civilizations.put(civilization.getName(), civilization);
        saveCivilizationsToFile();
    }

    /**
     * Sauvegarde toutes les civilisations dans le fichier YAML
     */
    private void saveCivilizationsToFile() {
        try {
            FileConfiguration config = new YamlConfiguration();
            ConfigurationSection civilizationsSection = config.createSection("civilizations");

            for (Civilization civilization : civilizations.values()) {
                ConfigurationSection civSection = civilizationsSection.createSection(civilization.getName());

                // Données de base
                civSection.set("owner-id", civilization.getOwnerId().toString());
                civSection.set("owner-name", civilization.getOwnerName());
                civSection.set("chat-enabled", civilization.isChatEnabled());
                civSection.set("creation-date", civilization.getCreationDate());

                // Home location
                Location home = civilization.getHomeLocation();
                if (home != null) {
                    ConfigurationSection homeSection = civSection.createSection("home");
                    homeSection.set("world", home.getWorld().getName());
                    homeSection.set("x", home.getX());
                    homeSection.set("y", home.getY());
                    homeSection.set("z", home.getZ());
                    homeSection.set("yaw", home.getYaw());
                    homeSection.set("pitch", home.getPitch());
                }

                // Membres
                List<String> membersList = new ArrayList<>();
                for (UUID member : civilization.getMembers()) {
                    membersList.add(member.toString());
                }
                civSection.set("members", membersList);

                // Chunks revendiqués
                civSection.set("claimed-chunks", new ArrayList<>(civilization.getClaimedChunks()));

                // Relations diplomatiques
                if (!civilization.getRelations().isEmpty()) {
                    ConfigurationSection relationsSection = civSection.createSection("relations");
                    for (Map.Entry<String, RelationType> entry : civilization.getRelations().entrySet()) {
                        relationsSection.set(entry.getKey(), entry.getValue().name());
                    }
                }
            }

            config.save(civilizationsFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Erreur lors de la sauvegarde des civilisations : " + e.getMessage());
            e.printStackTrace();
        }
    }

    public Civilization getCivilization(String name) {
        return civilizations.get(name);
    }

    public Collection<Civilization> getAllCivilizations() {
        return civilizations.values();
    }

    // ========== MÉTHODES POUR LES CHUNKS ==========

    /**
     * Sauvegarde un chunk revendiqué en mémoire et dans le fichier YAML
     */
    public void saveClaimedChunk(ClaimedChunk chunk) {
        String key = chunk.getWorldName() + "_" + chunk.getChunkX() + "_" + chunk.getChunkZ();
        claimedChunks.put(key, chunk);
        saveChunksToFile();
    }

    /**
     * Sauvegarde tous les chunks dans le fichier YAML
     */
    private void saveChunksToFile() {
        try {
            FileConfiguration config = new YamlConfiguration();
            ConfigurationSection chunksSection = config.createSection("chunks");

            for (ClaimedChunk chunk : claimedChunks.values()) {
                String chunkKey = chunk.getWorldName() + "_" + chunk.getChunkX() + "_" + chunk.getChunkZ();
                ConfigurationSection chunkSection = chunksSection.createSection(chunkKey);

                chunkSection.set("civilization", chunk.getCivilizationName());
                chunkSection.set("claim-date", chunk.getClaimDate());
            }

            config.save(chunksFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Erreur lors de la sauvegarde des chunks : " + e.getMessage());
            e.printStackTrace();
        }
    }

    public ClaimedChunk getClaimedChunk(String world, int x, int z) {
        String key = world + "_" + x + "_" + z;
        return claimedChunks.get(key);
    }

    public void removeClaimedChunk(String world, int x, int z) {
        String key = world + "_" + x + "_" + z;
        claimedChunks.remove(key);
        saveChunksToFile();
    }

    public Collection<ClaimedChunk> getAllClaimedChunks() {
        return claimedChunks.values();
    }

    // ========== MÉTHODES POUR LES JOUEURS ==========

    /**
     * Définit la civilisation d'un joueur
     */
    public void setPlayerCivilization(UUID playerId, String civilizationName) {
        if (civilizationName == null) {
            playerCivilizations.remove(playerId);
        } else {
            playerCivilizations.put(playerId, civilizationName);
        }
        savePlayersToFile();
    }

    /**
     * Sauvegarde tous les joueurs dans le fichier YAML
     */
    private void savePlayersToFile() {
        try {
            FileConfiguration config = new YamlConfiguration();
            ConfigurationSection playersSection = config.createSection("players");

            for (Map.Entry<UUID, String> entry : playerCivilizations.entrySet()) {
                ConfigurationSection playerSection = playersSection.createSection(entry.getKey().toString());
                playerSection.set("civilization", entry.getValue());
            }

            config.save(playersFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Erreur lors de la sauvegarde des joueurs : " + e.getMessage());
            e.printStackTrace();
        }
    }

    public String getPlayerCivilization(UUID playerId) {
        return playerCivilizations.get(playerId);
    }

    public Map<UUID, String> getAllPlayerCivilizations() {
        return new HashMap<>(playerCivilizations);
    }
}
