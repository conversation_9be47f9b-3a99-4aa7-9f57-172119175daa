package fr.civilizationmc.listeners;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Civilization;
import fr.civilizationmc.models.ClaimedChunk;
import fr.civilizationmc.utils.MessageUtils;
import org.bukkit.Chunk;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;

public class ChunkListener implements Listener {
    
    private final CivilizationMc plugin;
    
    public ChunkListener(CivilizationMc plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        Chunk chunk = event.getBlock().getChunk();
        
        if (!plugin.getChunkManager().canPlayerBuild(player, chunk)) {
            event.setCancelled(true);
            
            ClaimedChunk claimedChunk = plugin.getChunkManager().getClaimedChunk(chunk);
            if (claimedChunk != null) {
                MessageUtils.sendError(player, "Ce chunk appartient à la civilisation " + 
                    MessageUtils.formatCivilizationName(claimedChunk.getCivilizationName()) + "!");
            }
        }
    }
    
    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        Chunk chunk = event.getBlock().getChunk();
        
        if (!plugin.getChunkManager().canPlayerBuild(player, chunk)) {
            event.setCancelled(true);
            
            ClaimedChunk claimedChunk = plugin.getChunkManager().getClaimedChunk(chunk);
            if (claimedChunk != null) {
                MessageUtils.sendError(player, "Ce chunk appartient à la civilisation " + 
                    MessageUtils.formatCivilizationName(claimedChunk.getCivilizationName()) + "!");
            }
        }
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getClickedBlock() == null) {
            return;
        }
        
        Player player = event.getPlayer();
        Chunk chunk = event.getClickedBlock().getChunk();
        
        if (!plugin.getChunkManager().canPlayerBuild(player, chunk)) {
            event.setCancelled(true);
            
            ClaimedChunk claimedChunk = plugin.getChunkManager().getClaimedChunk(chunk);
            if (claimedChunk != null) {
                MessageUtils.sendError(player, "Ce chunk appartient à la civilisation " + 
                    MessageUtils.formatCivilizationName(claimedChunk.getCivilizationName()) + "!");
            }
        }
    }
    
    @EventHandler
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        if (!(event.getDamager() instanceof Player) || !(event.getEntity() instanceof Player)) {
            return;
        }
        
        Player attacker = (Player) event.getDamager();
        Player victim = (Player) event.getEntity();
        Chunk chunk = victim.getLocation().getChunk();
        
        // Vérifier si le PvP est autorisé dans ce chunk
        if (!plugin.getChunkManager().isPvpAllowed(chunk)) {
            event.setCancelled(true);
            MessageUtils.sendError(attacker, "Le PvP n'est pas autorisé dans les chunks revendiqués!");
            return;
        }
        
        // Vérifier les relations diplomatiques
        if (plugin.getDiplomacyManager().arePlayersAllied(attacker, victim)) {
            event.setCancelled(true);
            MessageUtils.sendError(attacker, "Vous ne pouvez pas attaquer un allié!");
        }
    }
}
