package fr.civilizationmc.models;

import java.util.Objects;
import java.util.UUID;

public class ClaimedChunk {
    
    private final String worldName;
    private final int chunkX;
    private final int chunkZ;
    private final String civilizationName;
    private final UUID claimedBy;
    private final long claimDate;
    
    public ClaimedChunk(String worldName, int chunkX, int chunkZ, String civilizationName, UUID claimedBy) {
        this.worldName = worldName;
        this.chunkX = chunkX;
        this.chunkZ = chunkZ;
        this.civilizationName = civilizationName;
        this.claimedBy = claimedBy;
        this.claimDate = System.currentTimeMillis();
    }
    
    public String getWorldName() {
        return worldName;
    }
    
    public int getChunkX() {
        return chunkX;
    }
    
    public int getChunkZ() {
        return chunkZ;
    }
    
    public String getCivilizationName() {
        return civilizationName;
    }
    
    public UUID getClaimedBy() {
        return claimedBy;
    }
    
    public long getClaimDate() {
        return claimDate;
    }
    
    /**
     * Génère une clé unique pour ce chunk
     * @return La clé sous format "monde_x_z"
     */
    public String getChunkKey() {
        return worldName + "_" + chunkX + "_" + chunkZ;
    }

    /**
     * Crée une clé de chunk à partir des coordonnées
     * @param worldName Nom du monde
     * @param chunkX Coordonnée X du chunk
     * @param chunkZ Coordonnée Z du chunk
     * @return La clé du chunk
     */
    public static String createChunkKey(String worldName, int chunkX, int chunkZ) {
        return worldName + "_" + chunkX + "_" + chunkZ;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ClaimedChunk that = (ClaimedChunk) obj;
        return chunkX == that.chunkX &&
               chunkZ == that.chunkZ &&
               Objects.equals(worldName, that.worldName);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(worldName, chunkX, chunkZ);
    }
    
    @Override
    public String toString() {
        return "ClaimedChunk{" +
                "world='" + worldName + '\'' +
                ", x=" + chunkX +
                ", z=" + chunkZ +
                ", civilization='" + civilizationName + '\'' +
                '}';
    }
}
