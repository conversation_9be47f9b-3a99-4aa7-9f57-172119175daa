package fr.civilizationmc.utils;

import fr.civilizationmc.CivilizationMc;
import org.bukkit.configuration.file.FileConfiguration;

public class ConfigUtils {
    
    private static CivilizationMc plugin;
    
    public static void initialize(CivilizationMc plugin) {
        ConfigUtils.plugin = plugin;
    }
    
    private static FileConfiguration getConfig() {
        return plugin.getConfig();
    }
    
    // Paramètres par défaut
    public static final int DEFAULT_MAX_CLAIMS_PER_CIVILIZATION = 50;
    public static final int DEFAULT_MAX_MEMBERS_PER_CIVILIZATION = 20;
    public static final int DEFAULT_MIN_CIVILIZATION_NAME_LENGTH = 3;
    public static final int DEFAULT_MAX_CIVILIZATION_NAME_LENGTH = 16;
    public static final boolean DEFAULT_ALLOW_PVP_IN_CLAIMED_CHUNKS = false;
    public static final boolean DEFAULT_ALLOW_CHUNK_UNCLAIM = true;

    // Paramètres par défaut pour le scoreboard
    public static final boolean DEFAULT_SCOREBOARD_ENABLED = true;
    public static final boolean DEFAULT_SCOREBOARD_SHOW_RELATIONS = true;
    public static final boolean DEFAULT_SCOREBOARD_SHOW_CREATION_DATE = true;
    public static final boolean DEFAULT_SCOREBOARD_SHOW_CHAT_STATUS = true;
    
    // Getters pour les valeurs de configuration
    public static int getMaxClaimsPerCivilization() {
        return getConfig().getInt("limits.max-claims-per-civilization", DEFAULT_MAX_CLAIMS_PER_CIVILIZATION);
    }
    
    public static int getMaxMembersPerCivilization() {
        return getConfig().getInt("limits.max-members-per-civilization", DEFAULT_MAX_MEMBERS_PER_CIVILIZATION);
    }
    
    public static int getMinCivilizationNameLength() {
        return getConfig().getInt("civilization.min-name-length", DEFAULT_MIN_CIVILIZATION_NAME_LENGTH);
    }
    
    public static int getMaxCivilizationNameLength() {
        return getConfig().getInt("civilization.max-name-length", DEFAULT_MAX_CIVILIZATION_NAME_LENGTH);
    }
    
    public static boolean isPvpAllowedInClaimedChunks() {
        return getConfig().getBoolean("gameplay.allow-pvp-in-claimed-chunks", DEFAULT_ALLOW_PVP_IN_CLAIMED_CHUNKS);
    }
    
    public static boolean isChunkUnclaimAllowed() {
        return getConfig().getBoolean("gameplay.allow-chunk-unclaim", DEFAULT_ALLOW_CHUNK_UNCLAIM);
    }
    
    public static boolean isWorldEnabled(String worldName) {
        return getConfig().getStringList("enabled-worlds").isEmpty() || 
               getConfig().getStringList("enabled-worlds").contains(worldName);
    }
    
    public static boolean isWorldDisabled(String worldName) {
        return getConfig().getStringList("disabled-worlds").contains(worldName);
    }

    // Getters pour les paramètres du scoreboard
    public static boolean isScoreboardEnabled() {
        return getConfig().getBoolean("scoreboard.enabled", DEFAULT_SCOREBOARD_ENABLED);
    }
    
    public static boolean shouldShowRelationsInScoreboard() {
        return getConfig().getBoolean("scoreboard.show-relations", DEFAULT_SCOREBOARD_SHOW_RELATIONS);
    }
    
    public static boolean shouldShowCreationDateInScoreboard() {
        return getConfig().getBoolean("scoreboard.show-creation-date", DEFAULT_SCOREBOARD_SHOW_CREATION_DATE);
    }
    
    public static boolean shouldShowChatStatusInScoreboard() {
        return getConfig().getBoolean("scoreboard.show-chat-status", DEFAULT_SCOREBOARD_SHOW_CHAT_STATUS);
    }
    
    // Messages configurables
    public static String getMessage(String key, String defaultMessage) {
        return MessageUtils.colorize(getConfig().getString("messages." + key, defaultMessage));
    }
    
    public static String getCivilizationCreatedMessage() {
        return getMessage("civilization-created", "&aVotre civilisation &6{name} &aa été créée avec succès!");
    }
    
    public static String getCivilizationAlreadyExistsMessage() {
        return getMessage("civilization-already-exists", "&cUne civilisation avec ce nom existe déjà!");
    }
    
    public static String getPlayerAlreadyInCivilizationMessage() {
        return getMessage("player-already-in-civilization", "&cVous êtes déjà membre d'une civilisation!");
    }
    
    public static String getChunkClaimedMessage() {
        return getMessage("chunk-claimed", "&aChunk revendiqué avec succès!");
    }
    
    public static String getChunkAlreadyClaimedMessage() {
        return getMessage("chunk-already-claimed", "&cCe chunk est déjà revendiqué par {civilization}!");
    }
    
    public static String getHomeSetMessage() {
        return getMessage("home-set", "&aHome défini avec succès!");
    }
    
    public static String getHomeTeleportMessage() {
        return getMessage("home-teleport", "&aTéléportation au home...");
    }
    
    public static String getNoHomeSetMessage() {
        return getMessage("no-home-set", "&cAucun home n'est défini pour votre civilisation!");
    }
}
