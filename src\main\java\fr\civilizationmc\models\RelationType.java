package fr.civilizationmc.models;

public enum RelationType {
    NEUTRAL("Neutre"),
    ALLY("Allié"),
    WAR("En guerre"),
    PEACE_PROPOSAL("Proposition de paix"),
    ALLIANCE_PROPOSAL("Proposition d'alliance");
    
    private final String displayName;
    
    RelationType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public boolean isHostile() {
        return this == WAR;
    }
    
    public boolean isFriendly() {
        return this == ALLY;
    }
    
    public boolean isProposal() {
        return this == PEACE_PROPOSAL || this == ALLIANCE_PROPOSAL;
    }
}
