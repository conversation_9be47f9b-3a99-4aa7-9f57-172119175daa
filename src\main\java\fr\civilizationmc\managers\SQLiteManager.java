package fr.civilizationmc.managers;

import fr.civilizationmc.CivilizationMc;
import org.bukkit.Bukkit;
import org.bukkit.Location;

import java.io.File;
import java.sql.*;
import java.util.UUID;

/**
 * Gestionnaire SQLite pour la base de données
 */
public class SQLiteManager {
    
    private final CivilizationMc plugin;
    private Connection connection;
    private final String databasePath;
    
    public SQLiteManager(CivilizationMc plugin) {
        this.plugin = plugin;
        this.databasePath = new File(plugin.getDataFolder(), "civilizations.db").getAbsolutePath();
    }
    
    /**
     * Initialise la connexion à la base de données
     */
    public void initialize() {
        try {
            // Créer le dossier si nécessaire
            File dataFolder = plugin.getDataFolder();
            if (!dataFolder.exists()) {
                dataFolder.mkdirs();
            }
            
            // Connexion à SQLite
            Class.forName("org.sqlite.JDBC");
            connection = DriverManager.getConnection("jdbc:sqlite:" + databasePath);
            
            // Activer les clés étrangères
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("PRAGMA foreign_keys = ON");
            }
            
            // Créer les tables
            createTables();
            
            plugin.getLogger().info("Base de données SQLite initialisée : " + databasePath);
            
        } catch (Exception e) {
            plugin.getLogger().severe("Erreur lors de l'initialisation de SQLite : " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Crée les tables de la base de données
     */
    private void createTables() throws SQLException {
        try (Statement stmt = connection.createStatement()) {
            
            // Table des civilisations
            stmt.execute("""
                CREATE TABLE IF NOT EXISTS civilizations (
                    name TEXT PRIMARY KEY,
                    owner_id TEXT NOT NULL,
                    owner_name TEXT NOT NULL,
                    home_world TEXT,
                    home_x REAL,
                    home_y REAL,
                    home_z REAL,
                    home_yaw REAL,
                    home_pitch REAL,
                    chat_enabled INTEGER DEFAULT 0,
                    creation_date INTEGER NOT NULL
                )
            """);
            
            // Table des membres
            stmt.execute("""
                CREATE TABLE IF NOT EXISTS civilization_members (
                    civilization_name TEXT NOT NULL,
                    player_id TEXT NOT NULL,
                    PRIMARY KEY (civilization_name, player_id),
                    FOREIGN KEY (civilization_name) REFERENCES civilizations(name) ON DELETE CASCADE
                )
            """);
            
            // Table des chunks
            stmt.execute("""
                CREATE TABLE IF NOT EXISTS claimed_chunks (
                    world TEXT NOT NULL,
                    x INTEGER NOT NULL,
                    z INTEGER NOT NULL,
                    civilization_name TEXT NOT NULL,
                    claim_date INTEGER NOT NULL,
                    PRIMARY KEY (world, x, z),
                    FOREIGN KEY (civilization_name) REFERENCES civilizations(name) ON DELETE CASCADE
                )
            """);
            
            // Table des relations diplomatiques
            stmt.execute("""
                CREATE TABLE IF NOT EXISTS diplomatic_relations (
                    civilization_name TEXT NOT NULL,
                    target_civilization TEXT NOT NULL,
                    relation_type TEXT NOT NULL,
                    PRIMARY KEY (civilization_name, target_civilization),
                    FOREIGN KEY (civilization_name) REFERENCES civilizations(name) ON DELETE CASCADE
                )
            """);
            
            // Table des joueurs (pour stocker leur civilisation actuelle)
            stmt.execute("""
                CREATE TABLE IF NOT EXISTS players (
                    player_id TEXT PRIMARY KEY,
                    civilization_name TEXT,
                    FOREIGN KEY (civilization_name) REFERENCES civilizations(name) ON DELETE SET NULL
                )
            """);
            
            plugin.getLogger().info("Tables SQLite créées avec succès");
        }
    }
    
    /**
     * Ferme la connexion à la base de données
     */
    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                plugin.getLogger().info("Connexion SQLite fermée");
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur lors de la fermeture de SQLite : " + e.getMessage());
        }
    }
    
    /**
     * Obtient la connexion à la base de données
     */
    public Connection getConnection() {
        try {
            // Vérifier si la connexion est encore valide
            if (connection == null || connection.isClosed()) {
                initialize();
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur lors de la vérification de la connexion : " + e.getMessage());
        }
        return connection;
    }
    
    /**
     * Exécute une requête de mise à jour (INSERT, UPDATE, DELETE)
     */
    public boolean executeUpdate(String sql, Object... params) {
        try (PreparedStatement stmt = getConnection().prepareStatement(sql)) {
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            stmt.executeUpdate();
            return true;
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur SQL : " + e.getMessage());
            plugin.getLogger().warning("Requête : " + sql);
            return false;
        }
    }
    
    /**
     * Exécute une requête de sélection
     */
    public ResultSet executeQuery(String sql, Object... params) {
        try {
            PreparedStatement stmt = getConnection().prepareStatement(sql);
            for (int i = 0; i < params.length; i++) {
                stmt.setObject(i + 1, params[i]);
            }
            return stmt.executeQuery();
        } catch (SQLException e) {
            plugin.getLogger().warning("Erreur SQL : " + e.getMessage());
            plugin.getLogger().warning("Requête : " + sql);
            return null;
        }
    }
}
