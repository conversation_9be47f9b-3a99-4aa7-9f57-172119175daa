package fr.civilizationmc.managers;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.RelationType;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.util.List;
import java.util.UUID;

/**
 * Gestionnaire de migration des données YAML vers SQLite
 */
public class YamlMigrator {
    
    private final CivilizationMc plugin;
    private final SQLiteManager sqliteManager;
    private File dataFolder;
    
    public YamlMigrator(CivilizationMc plugin, SQLiteManager sqliteManager) {
        this.plugin = plugin;
        this.sqliteManager = sqliteManager;
        this.dataFolder = new File(plugin.getDataFolder(), "data");
    }
    
    /**
     * Vérifie si une migration est nécessaire
     */
    public boolean needsMigration() {
        // Vérifier si les fichiers YAML existent et contiennent des données
        File civilizationsFile = new File(dataFolder, "civilizations.yml");
        File chunksFile = new File(dataFolder, "chunks.yml");
        File playersFile = new File(dataFolder, "players.yml");
        
        if (!civilizationsFile.exists() && !chunksFile.exists() && !playersFile.exists()) {
            return false; // Pas de fichiers YAML, pas de migration nécessaire
        }
        
        // Vérifier si la base SQLite est vide
        try {
            var rs = sqliteManager.executeQuery("SELECT COUNT(*) FROM civilizations");
            if (rs != null && rs.next()) {
                int count = rs.getInt(1);
                rs.close();
                return count == 0; // Migration nécessaire si SQLite est vide mais YAML existe
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Erreur lors de la vérification de migration : " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * Effectue la migration des données YAML vers SQLite
     */
    public void migrate() {
        try {
            migrateCivilizations();
            migrateChunks();
            migratePlayers();
            
            // Créer un fichier de marqueur pour indiquer que la migration est terminée
            File migrationMarker = new File(plugin.getDataFolder(), ".migrated");
            migrationMarker.createNewFile();
            
            plugin.getLogger().info("Migration YAML vers SQLite terminée avec succès!");
            
        } catch (Exception e) {
            plugin.getLogger().severe("Erreur lors de la migration : " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void migrateCivilizations() {
        File civilizationsFile = new File(dataFolder, "civilizations.yml");
        if (!civilizationsFile.exists()) return;
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(civilizationsFile);
        ConfigurationSection civilizationsSection = config.getConfigurationSection("civilizations");
        
        if (civilizationsSection == null) return;
        
        for (String name : civilizationsSection.getKeys(false)) {
            ConfigurationSection civSection = civilizationsSection.getConfigurationSection(name);
            if (civSection == null) continue;
            
            try {
                String ownerId = civSection.getString("owner-id");
                String ownerName = civSection.getString("owner-name");
                boolean chatEnabled = civSection.getBoolean("chat-enabled", false);
                long creationDate = civSection.getLong("creation-date", System.currentTimeMillis());
                
                // Insérer la civilisation
                String sql = """
                    INSERT OR REPLACE INTO civilizations 
                    (name, owner_id, owner_name, chat_enabled, creation_date) 
                    VALUES (?, ?, ?, ?, ?)
                """;
                sqliteManager.executeUpdate(sql, name, ownerId, ownerName, chatEnabled ? 1 : 0, creationDate);
                
                // Migrer le home si présent
                if (civSection.contains("home")) {
                    ConfigurationSection homeSection = civSection.getConfigurationSection("home");
                    if (homeSection != null) {
                        String world = homeSection.getString("world");
                        double x = homeSection.getDouble("x");
                        double y = homeSection.getDouble("y");
                        double z = homeSection.getDouble("z");
                        float yaw = (float) homeSection.getDouble("yaw");
                        float pitch = (float) homeSection.getDouble("pitch");
                        
                        String updateSql = """
                            UPDATE civilizations 
                            SET home_world = ?, home_x = ?, home_y = ?, home_z = ?, home_yaw = ?, home_pitch = ? 
                            WHERE name = ?
                        """;
                        sqliteManager.executeUpdate(updateSql, world, x, y, z, yaw, pitch, name);
                    }
                }
                
                // Migrer les membres
                List<String> membersList = civSection.getStringList("members");
                for (String memberStr : membersList) {
                    String memberSql = "INSERT OR REPLACE INTO civilization_members (civilization_name, player_id) VALUES (?, ?)";
                    sqliteManager.executeUpdate(memberSql, name, memberStr);
                }
                
                // Migrer les relations diplomatiques
                ConfigurationSection relationsSection = civSection.getConfigurationSection("relations");
                if (relationsSection != null) {
                    for (String targetCiv : relationsSection.getKeys(false)) {
                        String relationStr = relationsSection.getString(targetCiv);
                        String relationSql = "INSERT OR REPLACE INTO diplomatic_relations (civilization_name, target_civilization, relation_type) VALUES (?, ?, ?)";
                        sqliteManager.executeUpdate(relationSql, name, targetCiv, relationStr);
                    }
                }
                
                plugin.getLogger().info("Civilisation migrée : " + name);
                
            } catch (Exception e) {
                plugin.getLogger().warning("Erreur lors de la migration de la civilisation " + name + " : " + e.getMessage());
            }
        }
    }
    
    private void migrateChunks() {
        File chunksFile = new File(dataFolder, "chunks.yml");
        if (!chunksFile.exists()) return;
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(chunksFile);
        ConfigurationSection chunksSection = config.getConfigurationSection("chunks");
        
        if (chunksSection == null) return;
        
        for (String chunkKey : chunksSection.getKeys(false)) {
            ConfigurationSection chunkSection = chunksSection.getConfigurationSection(chunkKey);
            if (chunkSection == null) continue;
            
            try {
                String[] parts = chunkKey.split("_");
                if (parts.length != 3) continue;
                
                String world = parts[0];
                int x = Integer.parseInt(parts[1]);
                int z = Integer.parseInt(parts[2]);
                String civilizationName = chunkSection.getString("civilization");
                long claimDate = chunkSection.getLong("claim-date", System.currentTimeMillis());
                
                String sql = "INSERT OR REPLACE INTO claimed_chunks (world, x, z, civilization_name, claim_date) VALUES (?, ?, ?, ?, ?)";
                sqliteManager.executeUpdate(sql, world, x, z, civilizationName, claimDate);
                
            } catch (Exception e) {
                plugin.getLogger().warning("Erreur lors de la migration du chunk " + chunkKey + " : " + e.getMessage());
            }
        }
    }
    
    private void migratePlayers() {
        File playersFile = new File(dataFolder, "players.yml");
        if (!playersFile.exists()) return;
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(playersFile);
        ConfigurationSection playersSection = config.getConfigurationSection("players");
        
        if (playersSection == null) return;
        
        for (String playerId : playersSection.getKeys(false)) {
            try {
                String civilizationName = playersSection.getString(playerId + ".civilization");
                
                String sql = "INSERT OR REPLACE INTO players (player_id, civilization_name) VALUES (?, ?)";
                sqliteManager.executeUpdate(sql, playerId, civilizationName);
                
            } catch (Exception e) {
                plugin.getLogger().warning("Erreur lors de la migration du joueur " + playerId + " : " + e.getMessage());
            }
        }
    }
}
