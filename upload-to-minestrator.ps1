# Script PowerShell pour uploader le plugin vers Minestrator via SFTP
param(
    [Parameter(Mandatory=$true)]
    [string]$JarFile
)

# Configuration SFTP
$SftpHost = "9001.mystrator.com"
$SftpPort = 2022
$SftpUser = "pixel.1b4a4c1b"
$SftpPassword = "6408Fri464Jul-MSR"
$RemotePath = "/plugins/"

Write-Host "Upload du plugin vers Minestrator..." -ForegroundColor Green
Write-Host "Fichier: $JarFile" -ForegroundColor Yellow
Write-Host "Destination: $SftpUser@${SftpHost}:${SftpPort}${RemotePath}" -ForegroundColor Yellow

try {
    # Utiliser WinSCP si disponible
    if (Get-Command "WinSCP.exe" -ErrorAction SilentlyContinue) {
        Write-Host "Utilisation de WinSCP..." -ForegroundColor Cyan
        
        $WinSCPScript = @"
open sftp://$SftpUser`:$SftpPassword@$SftpHost`:$SftpPort
cd $RemotePath
put `"$JarFile`"
exit
"@
        
        $ScriptFile = [System.IO.Path]::GetTempFileName() + ".txt"
        $WinSCPScript | Out-File -FilePath $ScriptFile -Encoding ASCII
        
        & "WinSCP.exe" /script="$ScriptFile"
        Remove-Item $ScriptFile
        
        Write-Host "Upload terminé avec WinSCP!" -ForegroundColor Green
    }
    # Utiliser PSFTP si disponible
    elseif (Get-Command "psftp.exe" -ErrorAction SilentlyContinue) {
        Write-Host "Utilisation de PSFTP..." -ForegroundColor Cyan
        
        $PSFTPScript = @"
open $SftpHost -P $SftpPort -l $SftpUser -pw $SftpPassword
cd $RemotePath
put `"$JarFile`"
quit
"@
        
        $ScriptFile = [System.IO.Path]::GetTempFileName() + ".txt"
        $PSFTPScript | Out-File -FilePath $ScriptFile -Encoding ASCII
        
        & "psftp.exe" -batch -b $ScriptFile
        Remove-Item $ScriptFile
        
        Write-Host "Upload terminé avec PSFTP!" -ForegroundColor Green
    }
    # Utiliser PowerShell avec WinSCP .NET Assembly si disponible
    else {
        Write-Host "Tentative d'utilisation de PowerShell natif..." -ForegroundColor Cyan
        Write-Host "ATTENTION: Pour un upload automatique, installez WinSCP ou PuTTY PSFTP" -ForegroundColor Red
        Write-Host "Commande manuelle à exécuter:" -ForegroundColor Yellow
        Write-Host "scp -P $SftpPort `"$JarFile`" $SftpUser@$SftpHost`:$RemotePath" -ForegroundColor White
    }
}
catch {
    Write-Host "Erreur lors de l'upload: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Commande manuelle à exécuter:" -ForegroundColor Yellow
    Write-Host "scp -P $SftpPort `"$JarFile`" $SftpUser@$SftpHost`:$RemotePath" -ForegroundColor White
    exit 1
}
