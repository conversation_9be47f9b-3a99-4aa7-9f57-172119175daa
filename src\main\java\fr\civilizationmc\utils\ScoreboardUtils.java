package fr.civilizationmc.utils;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Civilization;
import fr.civilizationmc.models.ClaimedChunk;
import fr.civilizationmc.models.RelationType;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.scoreboard.DisplaySlot;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Scoreboard;
import org.bukkit.scoreboard.ScoreboardManager;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utilitaire pour gérer les scoreboards d'affichage des informations de civilisation
 */
public class ScoreboardUtils {
    
    private static final CivilizationMc plugin = CivilizationMc.getInstance();
    
    // Map pour stocker les scoreboards actifs par joueur
    private static final Map<UUID, Scoreboard> activeScoreboards = new ConcurrentHashMap<>();
    
    // Map pour stocker les scoreboards originaux des joueurs
    private static final Map<UUID, Scoreboard> originalScoreboards = new ConcurrentHashMap<>();
    
    /**
     * Affiche le scoreboard d'informations de civilisation pour un chunk
     */
    public static void showCivilizationScoreboard(Player player, ClaimedChunk claimedChunk) {
        // Vérifier si le système de scoreboard est activé
        if (!ConfigUtils.isScoreboardEnabled()) {
            return;
        }
        
        if (claimedChunk == null) {
            return;
        }
        
        // Récupérer la civilisation
        Civilization civilization = plugin.getCivilizationManager().getCivilization(claimedChunk.getCivilizationName());
        if (civilization == null) {
            return;
        }
        
        // Sauvegarder le scoreboard original si ce n'est pas déjà fait
        if (!originalScoreboards.containsKey(player.getUniqueId())) {
            originalScoreboards.put(player.getUniqueId(), player.getScoreboard());
        }
        
        // Créer un nouveau scoreboard
        ScoreboardManager manager = Bukkit.getScoreboardManager();
        Scoreboard scoreboard = manager.getNewScoreboard();
        
        // Créer l'objectif principal
        Objective objective = scoreboard.registerNewObjective("civilization", "dummy", 
            ChatColor.GOLD + "§l" + civilization.getName());
        objective.setDisplaySlot(DisplaySlot.SIDEBAR);
        
        int currentScore = 15;
        
        // Ligne vide pour l'espacement
        objective.getScore(" ").setScore(currentScore--);
        
        // Informations de base
        objective.getScore(ChatColor.YELLOW + "Propriétaire:").setScore(currentScore--);
        objective.getScore(ChatColor.WHITE + civilization.getOwnerName()).setScore(currentScore--);
        
        objective.getScore("  ").setScore(currentScore--);
        
        // Nombre de membres
        objective.getScore(ChatColor.YELLOW + "Membres: " + ChatColor.WHITE + civilization.getMemberCount()).setScore(currentScore--);
        
        // Nombre de chunks
        objective.getScore(ChatColor.YELLOW + "Chunks: " + ChatColor.WHITE + civilization.getClaimedChunkCount()).setScore(currentScore--);
        
        objective.getScore("   ").setScore(currentScore--);
        
        // Date de création (si activée dans la config)
        if (ConfigUtils.shouldShowCreationDateInScoreboard()) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
            String creationDate = dateFormat.format(new Date(civilization.getCreationDate()));
            objective.getScore(ChatColor.YELLOW + "Créée le:").setScore(currentScore--);
            objective.getScore(ChatColor.WHITE + creationDate).setScore(currentScore--);
            
            objective.getScore("    ").setScore(currentScore--);
        }
        
        // Relation avec le joueur actuel (si activée dans la config)
        if (ConfigUtils.shouldShowRelationsInScoreboard()) {
            Civilization playerCiv = plugin.getCivilizationManager().getPlayerCivilization(player.getUniqueId());
            if (playerCiv != null && !playerCiv.getName().equals(civilization.getName())) {
                RelationType relation = playerCiv.getRelation(civilization.getName());
                String relationText = getRelationText(relation);
                objective.getScore(ChatColor.YELLOW + "Relation:").setScore(currentScore--);
                objective.getScore(relationText).setScore(currentScore--);
                
                objective.getScore("     ").setScore(currentScore--);
            }
        }
        
        // Chat privé activé (si activé dans la config)
        if (ConfigUtils.shouldShowChatStatusInScoreboard()) {
            if (civilization.isChatEnabled()) {
                objective.getScore(ChatColor.GREEN + "Chat privé: ON").setScore(currentScore--);
            } else {
                objective.getScore(ChatColor.RED + "Chat privé: OFF").setScore(currentScore--);
            }
            
            objective.getScore("      ").setScore(currentScore--);
        }
        
        // Coordonnées du chunk
        String[] parts = claimedChunk.getChunkKey().split("_");
        if (parts.length == 3) {
            int chunkX = Integer.parseInt(parts[1]);
            int chunkZ = Integer.parseInt(parts[2]);
            objective.getScore(ChatColor.GRAY + "Chunk: " + chunkX + ", " + chunkZ).setScore(currentScore--);
        }
        
        // Appliquer le scoreboard au joueur
        player.setScoreboard(scoreboard);
        activeScoreboards.put(player.getUniqueId(), scoreboard);
    }
    
    /**
     * Masque le scoreboard de civilisation et restaure l'original
     */
    public static void hideCivilizationScoreboard(Player player) {
        UUID playerId = player.getUniqueId();
        
        // Supprimer le scoreboard actif
        activeScoreboards.remove(playerId);
        
        // Restaurer le scoreboard original
        Scoreboard originalScoreboard = originalScoreboards.get(playerId);
        if (originalScoreboard != null) {
            player.setScoreboard(originalScoreboard);
        } else {
            // Fallback vers le scoreboard principal du serveur
            player.setScoreboard(Bukkit.getScoreboardManager().getMainScoreboard());
        }
    }
    
    /**
     * Nettoie les scoreboards d'un joueur (à la déconnexion)
     */
    public static void cleanupPlayerScoreboards(Player player) {
        UUID playerId = player.getUniqueId();
        activeScoreboards.remove(playerId);
        originalScoreboards.remove(playerId);
    }
    
    /**
     * Vérifie si un joueur a un scoreboard de civilisation actif
     */
    public static boolean hasActiveScoreboard(Player player) {
        return activeScoreboards.containsKey(player.getUniqueId());
    }
    
    /**
     * Met à jour le scoreboard si le joueur en a un actif
     */
    public static void updateScoreboardIfActive(Player player, ClaimedChunk claimedChunk) {
        if (hasActiveScoreboard(player)) {
            showCivilizationScoreboard(player, claimedChunk);
        }
    }
    
    /**
     * Convertit un type de relation en texte coloré
     */
    private static String getRelationText(RelationType relation) {
        switch (relation) {
            case WAR:
                return ChatColor.DARK_RED + "⚔ Guerre";
            case PEACE_PROPOSAL:
                return ChatColor.BLUE + "☮ Proposition de paix";
            case ALLY:
                return ChatColor.GREEN + "⚡ Alliance";
            case ALLIANCE_PROPOSAL:
                return ChatColor.YELLOW + "⚡ Proposition d'alliance";
            case NEUTRAL:
            default:
                return ChatColor.GRAY + "⚬ Neutre";
        }
    }
}
