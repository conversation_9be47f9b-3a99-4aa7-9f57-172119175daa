# CivilizationMc Plugin

Un plugin Minecraft permettant aux joueurs de créer et gérer leurs propres civilisations avec des fonctionnalités avancées de revendication de territoire, gestion des membres, et diplomatie.

## Version
- **Version du plugin**: 1.0.0
- **Version Minecraft**: 1.21.1
- **API**: Spigot/Paper

## Fonctionnalités

### 🏛️ Gestion des Civilisations
- **Création**: `/cv create <nom>` - Créer une nouvelle civilisation
- **Informations**: `/cv info` - Voir les détails de votre civilisation
- **Liste**: `/cv list` - Afficher toutes les civilisations existantes

### 🗺️ Système de Chunks
- **Revendication**: `/cv claim` - Revendiquer le chunk actuel
- **Abandon**: `/cv unclaim` - Abandonner le chunk actuel
- Protection automatique contre la construction/destruction par les non-membres

### 🏠 Système de Home
- **Définir**: `/cv sethome` - Définir le point de spawn de la civilisation
- **Téléportation**: `/cv home` - Se téléporter au home de la civilisation

### 👥 Gestion des Membres
- **Invitation**: `/cv invite <joueur>` - Inviter un joueur à rejoindre
- **Rejoindre**: `/cv join` - Accepter une invitation (en développement)
- **Exclusion**: `/cv kick <joueur>` - Exclure un membre de la civilisation

### 💬 Chat Privé
- **Activation**: `/cv chat` - Activer/désactiver le chat privé de civilisation
- Messages automatiquement envoyés aux membres quand activé

### ⚔️ Système Diplomatique
- **Guerre**: `/cv war <civilisation>` - Déclarer la guerre à une civilisation
- **Paix**: `/cv peace <civilisation>` - Proposer un traité de paix
- **Alliance**: `/cv ally <civilisation>` - Proposer une alliance
- Protection automatique contre les attaques entre alliés

## Installation

1. Téléchargez le fichier `.jar` du plugin
2. Placez-le dans le dossier `plugins/` de votre serveur
3. Redémarrez le serveur
4. Configurez le plugin via le fichier `config.yml` généré

## Configuration

Le fichier `config.yml` permet de personnaliser :

### Limites
```yaml
limits:
  max_chunks_per_civilization: 50
  max_members_per_civilization: 20
```

### Gameplay
```yaml
gameplay:
  pvp_in_claimed_chunks: false
  allow_chunk_abandonment: true
```

### Mondes
```yaml
worlds:
  enabled_worlds:
    - world
    - world_nether
  disabled_worlds:
    - creative_world
```

### Base de Données
```yaml
database:
  type: sqlite  # ou mysql
  # Configuration MySQL si nécessaire
```

## Permissions

- `civilizationmc.create` - Créer une civilisation
- `civilizationmc.claim` - Revendiquer des chunks
- `civilizationmc.home` - Utiliser le système de home
- `civilizationmc.invite` - Inviter des joueurs
- `civilizationmc.diplomacy` - Utiliser les commandes diplomatiques
- `civilizationmc.admin` - Accès administrateur (toutes permissions)

## Architecture du Code

### Packages
- `commands/` - Gestion des commandes
- `managers/` - Gestionnaires des différents systèmes
- `models/` - Classes de données (Civilization, ClaimedChunk, etc.)
- `utils/` - Utilitaires et helpers
- `listeners/` - Event listeners
- `database/` - Gestion de la persistance (intégré dans DatabaseManager)

### Managers Principaux
- **CivilizationManager** - Gestion des civilisations et membres
- **ChunkManager** - Gestion des revendications de territoire
- **DiplomacyManager** - Gestion des relations diplomatiques
- **DatabaseManager** - Persistance des données

## Développement

### Prérequis
- Java 17+
- Maven 3.6+
- Spigot API 1.21.1

### Compilation
```bash
mvn clean package
```

Le fichier `.jar` sera généré dans le dossier `target/`.

### Structure des Données

#### Civilization
- Nom unique
- Propriétaire (UUID)
- Liste des membres
- Chunks revendiqués
- Position du home
- État du chat privé
- Relations diplomatiques

#### ClaimedChunk
- Coordonnées (monde, x, z)
- Civilisation propriétaire
- Joueur ayant revendiqué
- Date de revendication

## Roadmap

### Fonctionnalités Prévues
- [ ] Système d'invitations complet avec acceptation/refus
- [ ] Interface graphique pour la gestion des civilisations
- [ ] Système d'économie intégré
- [ ] Grades et permissions internes aux civilisations
- [ ] Événements et quêtes de civilisation
- [ ] API pour les développeurs tiers

### Améliorations Techniques
- [ ] Implémentation complète de la persistance MySQL
- [ ] Optimisation des performances pour les gros serveurs
- [ ] Tests unitaires complets
- [ ] Documentation API

## Support

Pour signaler des bugs ou demander des fonctionnalités :
1. Vérifiez que vous utilisez la dernière version
2. Consultez les logs du serveur pour les erreurs
3. Fournissez des informations détaillées sur le problème

## Licence

Ce plugin est développé pour un usage privé. Tous droits réservés.

---

**Développé avec ❤️ pour la communauté Minecraft**
