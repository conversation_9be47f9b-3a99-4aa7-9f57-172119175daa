package fr.civilizationmc;

import fr.civilizationmc.commands.CivilizationCommand;
import fr.civilizationmc.listeners.ChunkListener;
import fr.civilizationmc.listeners.PlayerListener;
import fr.civilizationmc.managers.ChunkManager;
import fr.civilizationmc.managers.CivilizationManager;
import fr.civilizationmc.managers.DatabaseManager;
import fr.civilizationmc.managers.DiplomacyManager;
import fr.civilizationmc.managers.InvitationManager;
import fr.civilizationmc.utils.ConfigUtils;
import fr.civilizationmc.utils.MessageUtils;
import org.bukkit.plugin.java.JavaPlugin;

public class CivilizationMc extends JavaPlugin {
    
    private static CivilizationMc instance;
    private DatabaseManager databaseManager;
    private CivilizationManager civilizationManager;
    private ChunkManager chunkManager;
    private DiplomacyManager diplomacyManager;
    private InvitationManager invitationManager;
    
    @Override
    public void onEnable() {
        instance = this;
        
        // Sauvegarder la configuration par défaut
        saveDefaultConfig();
        
        // Initialiser ConfigUtils avec la configuration
        ConfigUtils.initialize(this);
        
        // Initialiser les managers
        initializeManagers();
        
        // Enregistrer les commandes
        registerCommands();
        
        // Enregistrer les listeners
        registerListeners();
        
        getLogger().info("CivilizationMc v" + getDescription().getVersion() + " activé!");
        getLogger().info("Plugin développé pour Minecraft " + getDescription().getAPIVersion());
    }
    
    @Override
    public void onDisable() {
        // Sauvegarder les données avant la fermeture
        if (databaseManager != null) {
            databaseManager.close();
        }
        
        if (invitationManager != null) {
            invitationManager.shutdown();
        }
        
        getLogger().info("CivilizationMc désactivé!");
    }
    
    private void initializeManagers() {
        // Initialiser le gestionnaire de base de données
        databaseManager = new DatabaseManager(this);
        
        // Initialiser les autres gestionnaires (ils récupèrent databaseManager via plugin.getDatabaseManager())
        civilizationManager = new CivilizationManager(this);
        chunkManager = new ChunkManager(this);
        diplomacyManager = new DiplomacyManager(this);
        invitationManager = new InvitationManager(this);
        
        getLogger().info("Managers initialisés avec succès!");
    }
    
    private void registerCommands() {
        // Enregistrer la commande principale /cv
        CivilizationCommand civilizationCommand = new CivilizationCommand(this);
        getCommand("cv").setExecutor(civilizationCommand);
        getCommand("cv").setTabCompleter(civilizationCommand);
        
        getLogger().info("Commandes enregistrées avec succès!");
    }
    
    private void registerListeners() {
        // Enregistrer les listeners d'événements
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        getServer().getPluginManager().registerEvents(new ChunkListener(this), this);

        getLogger().info("Listeners enregistrés avec succès!");
    }
    
    // Getters pour accéder aux managers depuis d'autres classes
    public static CivilizationMc getInstance() {
        return instance;
    }
    
    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }
    
    public CivilizationManager getCivilizationManager() {
        return civilizationManager;
    }
    
    public ChunkManager getChunkManager() {
        return chunkManager;
    }
    
    public DiplomacyManager getDiplomacyManager() {
        return diplomacyManager;
    }
    
    public InvitationManager getInvitationManager() {
        return invitationManager;
    }
}
