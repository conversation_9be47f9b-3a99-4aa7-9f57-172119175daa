package fr.civilizationmc.managers;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Civilization;
import fr.civilizationmc.models.RelationType;
import org.bukkit.entity.Player;

public class DiplomacyManager {
    
    private final CivilizationMc plugin;
    private final DatabaseManager databaseManager;
    private final CivilizationManager civilizationManager;
    
    public DiplomacyManager(CivilizationMc plugin) {
        this.plugin = plugin;
        this.databaseManager = plugin.getDatabaseManager();
        this.civilizationManager = plugin.getCivilizationManager();
    }
    
    /**
     * Déclare la guerre à une autre civilisation
     */
    public boolean declareWar(Player player, String targetCivilizationName) {
        Civilization playerCivilization = civilizationManager.getPlayerCivilization(player.getUniqueId());
        if (playerCivilization == null) {
            return false; // Le joueur n'est pas dans une civilisation
        }
        
        if (!playerCivilization.isOwner(player.getUniqueId())) {
            return false; // Seul le propriétaire peut déclarer la guerre
        }
        
        Civilization targetCivilization = civilizationManager.getCivilization(targetCivilizationName);
        if (targetCivilization == null) {
            return false; // Civilisation cible introuvable
        }
        
        if (playerCivilization.getName().equals(targetCivilizationName)) {
            return false; // Ne peut pas déclarer la guerre à soi-même
        }
        
        // Définir la relation de guerre
        setRelation(playerCivilization, targetCivilization, RelationType.WAR);
        
        // Notifier les membres de la civilisation qui déclare la guerre
        fr.civilizationmc.utils.MessageUtils.sendCivilizationMessage(
            playerCivilization.getMembers(),
            "Guerre déclarée à " + fr.civilizationmc.utils.MessageUtils.formatCivilizationName(targetCivilizationName) + " !"
        );
        
        // Notifier les membres de la civilisation cible
        fr.civilizationmc.utils.MessageUtils.sendCivilizationMessage(
            targetCivilization.getMembers(),
            fr.civilizationmc.utils.MessageUtils.formatCivilizationName(playerCivilization.getName()) + " vous a déclaré la guerre !"
        );
        
        return true;
    }
    
    /**
     * Propose la paix à une autre civilisation
     */
    public boolean proposePeace(Player player, String targetCivilizationName) {
        Civilization playerCivilization = civilizationManager.getPlayerCivilization(player.getUniqueId());
        if (playerCivilization == null) {
            return false;
        }
        
        if (!playerCivilization.isOwner(player.getUniqueId())) {
            return false;
        }
        
        Civilization targetCivilization = civilizationManager.getCivilization(targetCivilizationName);
        if (targetCivilization == null) {
            return false;
        }
        
        if (playerCivilization.getName().equals(targetCivilizationName)) {
            return false;
        }
        
        // Vérifier si les civilisations sont en guerre
        RelationType currentRelation = playerCivilization.getRelation(targetCivilizationName);
        if (currentRelation != RelationType.WAR) {
            return false; // Pas en guerre, pas besoin de paix
        }
        
        // Proposer la paix
        playerCivilization.setRelation(targetCivilizationName, RelationType.PEACE_PROPOSAL);
        databaseManager.saveCivilization(playerCivilization);
        
        // Notifier les membres de la civilisation qui propose la paix
        fr.civilizationmc.utils.MessageUtils.sendCivilizationMessage(
            playerCivilization.getMembers(),
            "Proposition de paix envoyée à " + fr.civilizationmc.utils.MessageUtils.formatCivilizationName(targetCivilizationName)
        );
        
        // Notifier les membres de la civilisation cible
        fr.civilizationmc.utils.MessageUtils.sendCivilizationMessage(
            targetCivilization.getMembers(),
            fr.civilizationmc.utils.MessageUtils.formatCivilizationName(playerCivilization.getName()) + " vous propose la paix ! Utilisez /cv peace " + playerCivilization.getName() + " pour accepter."
        );
        
        return true;
    }
    
    /**
     * Accepte une proposition de paix
     */
    public boolean acceptPeace(Player player, String proposerCivilizationName) {
        Civilization playerCivilization = civilizationManager.getPlayerCivilization(player.getUniqueId());
        if (playerCivilization == null) {
            return false;
        }
        
        if (!playerCivilization.isOwner(player.getUniqueId())) {
            return false;
        }
        
        Civilization proposerCivilization = civilizationManager.getCivilization(proposerCivilizationName);
        if (proposerCivilization == null) {
            return false;
        }
        
        // Vérifier s'il y a une proposition de paix
        RelationType proposerRelation = proposerCivilization.getRelation(playerCivilization.getName());
        if (proposerRelation != RelationType.PEACE_PROPOSAL) {
            return false; // Pas de proposition de paix
        }
        
        // Accepter la paix - retour à la neutralité
        setRelation(playerCivilization, proposerCivilization, RelationType.NEUTRAL);
        
        return true;
    }
    
    /**
     * Propose une alliance à une autre civilisation
     */
    public boolean proposeAlliance(Player player, String targetCivilizationName) {
        Civilization playerCivilization = civilizationManager.getPlayerCivilization(player.getUniqueId());
        if (playerCivilization == null) {
            return false;
        }
        
        if (!playerCivilization.isOwner(player.getUniqueId())) {
            return false;
        }
        
        Civilization targetCivilization = civilizationManager.getCivilization(targetCivilizationName);
        if (targetCivilization == null) {
            return false;
        }
        
        if (playerCivilization.getName().equals(targetCivilizationName)) {
            return false;
        }
        
        // Vérifier que les civilisations ne sont pas en guerre
        RelationType currentRelation = playerCivilization.getRelation(targetCivilizationName);
        if (currentRelation == RelationType.WAR) {
            return false; // Ne peut pas proposer d'alliance en temps de guerre
        }
        
        // Proposer l'alliance
        playerCivilization.setRelation(targetCivilizationName, RelationType.ALLIANCE_PROPOSAL);
        databaseManager.saveCivilization(playerCivilization);
        
        // Notifier les membres de la civilisation qui propose l'alliance
        fr.civilizationmc.utils.MessageUtils.sendCivilizationMessage(
            playerCivilization.getMembers(),
            "Proposition d'alliance envoyée à " + fr.civilizationmc.utils.MessageUtils.formatCivilizationName(targetCivilizationName)
        );
        
        // Notifier les membres de la civilisation cible
        fr.civilizationmc.utils.MessageUtils.sendCivilizationMessage(
            targetCivilization.getMembers(),
            fr.civilizationmc.utils.MessageUtils.formatCivilizationName(playerCivilization.getName()) + " vous propose une alliance ! Utilisez /cv ally " + playerCivilization.getName() + " pour accepter."
        );
        
        return true;
    }
    
    /**
     * Accepte une proposition d'alliance
     */
    public boolean acceptAlliance(Player player, String proposerCivilizationName) {
        Civilization playerCivilization = civilizationManager.getPlayerCivilization(player.getUniqueId());
        if (playerCivilization == null) {
            return false;
        }
        
        if (!playerCivilization.isOwner(player.getUniqueId())) {
            return false;
        }
        
        Civilization proposerCivilization = civilizationManager.getCivilization(proposerCivilizationName);
        if (proposerCivilization == null) {
            return false;
        }
        
        // Vérifier s'il y a une proposition d'alliance
        RelationType proposerRelation = proposerCivilization.getRelation(playerCivilization.getName());
        if (proposerRelation != RelationType.ALLIANCE_PROPOSAL) {
            return false; // Pas de proposition d'alliance
        }
        
        // Accepter l'alliance
        setRelation(playerCivilization, proposerCivilization, RelationType.ALLY);
        
        return true;
    }
    
    /**
     * Rompt une alliance
     */
    public boolean breakAlliance(Player player, String targetCivilizationName) {
        Civilization playerCivilization = civilizationManager.getPlayerCivilization(player.getUniqueId());
        if (playerCivilization == null) {
            return false;
        }
        
        if (!playerCivilization.isOwner(player.getUniqueId())) {
            return false;
        }
        
        Civilization targetCivilization = civilizationManager.getCivilization(targetCivilizationName);
        if (targetCivilization == null) {
            return false;
        }
        
        // Vérifier s'il y a une alliance
        RelationType currentRelation = playerCivilization.getRelation(targetCivilizationName);
        if (currentRelation != RelationType.ALLY) {
            return false; // Pas d'alliance à rompre
        }
        
        // Rompre l'alliance - retour à la neutralité
        setRelation(playerCivilization, targetCivilization, RelationType.NEUTRAL);
        
        return true;
    }
    
    /**
     * Définit une relation bilatérale entre deux civilisations
     */
    private void setRelation(Civilization civ1, Civilization civ2, RelationType relationType) {
        civ1.setRelation(civ2.getName(), relationType);
        civ2.setRelation(civ1.getName(), relationType);
        
        databaseManager.saveCivilization(civ1);
        databaseManager.saveCivilization(civ2);
    }
    
    /**
     * Récupère la relation entre deux civilisations
     */
    public RelationType getRelation(String civilization1, String civilization2) {
        Civilization civ1 = civilizationManager.getCivilization(civilization1);
        if (civ1 == null) {
            return RelationType.NEUTRAL;
        }
        
        return civ1.getRelation(civilization2);
    }
    
    /**
     * Vérifie si deux civilisations sont alliées
     */
    public boolean areAllied(String civilization1, String civilization2) {
        return getRelation(civilization1, civilization2) == RelationType.ALLY;
    }
    
    /**
     * Vérifie si deux civilisations sont en guerre
     */
    public boolean areAtWar(String civilization1, String civilization2) {
        return getRelation(civilization1, civilization2) == RelationType.WAR;
    }
    
    /**
     * Vérifie si deux joueurs sont alliés (via leurs civilisations)
     */
    public boolean arePlayersAllied(Player player1, Player player2) {
        Civilization civ1 = plugin.getCivilizationManager().getPlayerCivilization(player1.getUniqueId());
        Civilization civ2 = plugin.getCivilizationManager().getPlayerCivilization(player2.getUniqueId());
        
        if (civ1 == null || civ2 == null) {
            return false;
        }
        
        // Même civilisation = alliés
        if (civ1.getName().equals(civ2.getName())) {
            return true;
        }
        
        // Vérifier les relations diplomatiques
        RelationType relation = civ1.getRelation(civ2.getName());
        return relation == RelationType.ALLY;
    }
    
    /**
     * Vérifie si deux joueurs sont en guerre (via leurs civilisations)
     */
    public boolean arePlayersAtWar(Player player1, Player player2) {
        Civilization civ1 = civilizationManager.getPlayerCivilization(player1.getUniqueId());
        Civilization civ2 = civilizationManager.getPlayerCivilization(player2.getUniqueId());
        
        if (civ1 == null || civ2 == null) {
            return false;
        }
        
        return areAtWar(civ1.getName(), civ2.getName());
    }
}
