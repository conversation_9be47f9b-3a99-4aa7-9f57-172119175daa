package fr.civilizationmc.managers;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Civilization;
import fr.civilizationmc.utils.ConfigUtils;
import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.UUID;

public class CivilizationManager {
    
    private final CivilizationMc plugin;
    private final DatabaseManager databaseManager;
    
    public CivilizationManager(CivilizationMc plugin) {
        this.plugin = plugin;
        this.databaseManager = plugin.getDatabaseManager();
    }
    
    /**
     * Crée une nouvelle civilisation
     */
    public boolean createCivilization(Player player, String name) {
        // Vérifications
        if (getPlayerCivilization(player.getUniqueId()) != null) {
            return false; // Le joueur est déjà dans une civilisation
        }
        
        if (civilizationExists(name)) {
            return false; // Une civilisation avec ce nom existe déjà
        }
        
        if (name.length() < ConfigUtils.getMinCivilizationNameLength() || 
            name.length() > ConfigUtils.getMaxCivilizationNameLength()) {
            return false; // Nom invalide
        }
        
        // Création de la civilisation
        Civilization civilization = new Civilization(name, player.getUniqueId(), player.getName());
        databaseManager.saveCivilization(civilization);
        databaseManager.setPlayerCivilization(player.getUniqueId(), name);
        
        return true;
    }
    

    
    /**
     * Définit le home d'une civilisation
     */
    public boolean setHome(Player player, Location location) {
        Civilization civilization = getPlayerCivilization(player.getUniqueId());
        if (civilization == null) {
            return false;
        }
        
        if (!civilization.isOwner(player.getUniqueId())) {
            return false; // Seul le propriétaire peut définir le home
        }
        
        civilization.setHomeLocation(location);
        databaseManager.saveCivilization(civilization);
        
        return true;
    }
    
    /**
     * Téléporte un joueur au home de sa civilisation
     */
    public boolean teleportToHome(Player player) {
        Civilization civilization = getPlayerCivilization(player.getUniqueId());
        if (civilization == null || civilization.getHomeLocation() == null) {
            return false;
        }
        
        player.teleport(civilization.getHomeLocation());
        return true;
    }
    
    /**
     * Invite un joueur dans une civilisation
     */
    public boolean invitePlayer(Player inviter, Player invited) {
        Civilization civilization = getPlayerCivilization(inviter.getUniqueId());
        if (civilization == null) {
            return false;
        }
        
        if (!civilization.isOwner(inviter.getUniqueId())) {
            return false; // Seul le propriétaire peut inviter
        }
        
        if (getPlayerCivilization(invited.getUniqueId()) != null) {
            return false; // Le joueur est déjà dans une civilisation
        }
        
        if (civilization.getMemberCount() >= ConfigUtils.getMaxMembersPerCivilization()) {
            return false; // Limite de membres atteinte
        }
        
        // TODO: Système d'invitations avec acceptation/refus
        return true;
    }
    
    /**
     * Ajoute un joueur à une civilisation existante
     */
    public boolean addPlayerToCivilization(UUID playerUuid, String civilizationName) {
        // Vérifier que le joueur n'est pas déjà dans une civilisation
        if (getPlayerCivilization(playerUuid) != null) {
            return false;
        }
        
        // Récupérer la civilisation
        Civilization civilization = databaseManager.getCivilization(civilizationName);
        if (civilization == null) {
            return false;
        }
        
        // Vérifier les limites
        if (civilization.getMemberCount() >= ConfigUtils.getMaxMembersPerCivilization()) {
            return false;
        }
        
        // Ajouter le joueur
        civilization.addMember(playerUuid);
        databaseManager.setPlayerCivilization(playerUuid, civilizationName);
        
        return true;
    }
    
    /**
     * Supprime un joueur d'une civilisation
     */
    public boolean removePlayerFromCivilization(UUID playerId, String civilizationName) {
        Civilization civilization = getCivilization(civilizationName);
        if (civilization == null) {
            return false;
        }
        
        if (!civilization.removeMember(playerId)) {
            return false; // Ne peut pas retirer le propriétaire
        }
        
        databaseManager.saveCivilization(civilization);
        databaseManager.setPlayerCivilization(playerId, null);
        
        return true;
    }
    
    /**
     * Active/désactive le chat privé d'une civilisation
     */
    public boolean toggleChat(Player player) {
        Civilization civilization = getPlayerCivilization(player.getUniqueId());
        if (civilization == null) {
            return false;
        }
        
        civilization.setChatEnabled(!civilization.isChatEnabled());
        databaseManager.saveCivilization(civilization);
        
        return true;
    }
    
    // Méthodes utilitaires
    public Civilization getCivilization(String name) {
        return databaseManager.getCivilization(name);
    }
    
    public Civilization getPlayerCivilization(UUID playerId) {
        String civilizationName = databaseManager.getPlayerCivilization(playerId);
        return civilizationName != null ? getCivilization(civilizationName) : null;
    }
    
    public boolean civilizationExists(String name) {
        return getCivilization(name) != null;
    }
    
    /**
     * Récupère toutes les civilisations existantes
     */
    public java.util.Collection<Civilization> getAllCivilizations() {
        return databaseManager.getAllCivilizations();
    }
}
