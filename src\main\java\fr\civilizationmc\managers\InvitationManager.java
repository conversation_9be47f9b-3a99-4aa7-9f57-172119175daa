package fr.civilizationmc.managers;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Invitation;
import fr.civilizationmc.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class InvitationManager {
    
    private final CivilizationMc plugin;
    private final Map<UUID, List<Invitation>> pendingInvitations;
    
    public InvitationManager(CivilizationMc plugin) {
        this.plugin = plugin;
        this.pendingInvitations = new ConcurrentHashMap<>();
        
        // Tâche de nettoyage des invitations expirées toutes les minutes
        startCleanupTask();
    }
    
    public boolean createInvitation(UUID inviterUuid, UUID invitedUuid, String civilizationName) {
        // Vérifier si le joueur invité a déjà une invitation de cette civilisation
        if (hasInvitation(invitedUuid, civilizationName)) {
            return false;
        }
        
        // Créer l'invitation
        Invitation invitation = new Invitation(invitedUuid, civilizationName, inviterUuid);
        
        // Ajouter à la liste des invitations du joueur
        pendingInvitations.computeIfAbsent(invitedUuid, k -> new ArrayList<>()).add(invitation);
        
        return true;
    }
    
    public boolean acceptInvitation(UUID playerUuid, String civilizationName) {
        List<Invitation> invitations = pendingInvitations.get(playerUuid);
        if (invitations == null) {
            return false;
        }
        
        // Trouver l'invitation correspondante
        Invitation invitation = invitations.stream()
                .filter(inv -> inv.getCivilizationName().equals(civilizationName) && !inv.isExpired())
                .findFirst()
                .orElse(null);
        
        if (invitation == null) {
            return false;
        }
        
        // Supprimer l'invitation
        invitations.remove(invitation);
        if (invitations.isEmpty()) {
            pendingInvitations.remove(playerUuid);
        }
        
        // Ajouter le joueur à la civilisation
        return plugin.getCivilizationManager().addPlayerToCivilization(playerUuid, civilizationName);
    }
    
    public boolean declineInvitation(UUID playerUuid, String civilizationName) {
        List<Invitation> invitations = pendingInvitations.get(playerUuid);
        if (invitations == null) {
            return false;
        }
        
        // Trouver et supprimer l'invitation
        boolean removed = invitations.removeIf(inv -> inv.getCivilizationName().equals(civilizationName));
        
        if (invitations.isEmpty()) {
            pendingInvitations.remove(playerUuid);
        }
        
        return removed;
    }
    
    public List<Invitation> getPlayerInvitations(UUID playerUuid) {
        List<Invitation> invitations = pendingInvitations.get(playerUuid);
        if (invitations == null) {
            return new ArrayList<>();
        }
        
        // Filtrer les invitations expirées
        invitations.removeIf(Invitation::isExpired);
        if (invitations.isEmpty()) {
            pendingInvitations.remove(playerUuid);
            return new ArrayList<>();
        }
        
        return new ArrayList<>(invitations);
    }
    
    /**
     * Obtient le nombre d'invitations en attente pour un joueur
     */
    public int getPendingInvitationsCount(UUID playerUuid) {
        return getPlayerInvitations(playerUuid).size();
    }
    
    public boolean hasInvitation(UUID playerUuid, String civilizationName) {
        List<Invitation> invitations = pendingInvitations.get(playerUuid);
        if (invitations == null) {
            return false;
        }
        
        return invitations.stream()
                .anyMatch(inv -> inv.getCivilizationName().equals(civilizationName) && !inv.isExpired());
    }
    
    public boolean hasAnyInvitation(UUID playerUuid) {
        List<Invitation> invitations = getPlayerInvitations(playerUuid);
        return !invitations.isEmpty();
    }
    
    public void removeAllInvitations(UUID playerUuid) {
        pendingInvitations.remove(playerUuid);
    }
    
    public void removeInvitationsForCivilization(String civilizationName) {
        pendingInvitations.values().forEach(invitations -> 
            invitations.removeIf(inv -> inv.getCivilizationName().equals(civilizationName))
        );
        
        // Nettoyer les listes vides
        pendingInvitations.entrySet().removeIf(entry -> entry.getValue().isEmpty());
    }
    
    public void sendInvitationList(Player player) {
        List<Invitation> invitations = getPlayerInvitations(player.getUniqueId());
        
        if (invitations.isEmpty()) {
            MessageUtils.sendInfo(player, "Vous n'avez aucune invitation en attente.");
            return;
        }
        
        MessageUtils.sendMessage(player, "=== Invitations en attente ===");
        for (int i = 0; i < invitations.size(); i++) {
            Invitation invitation = invitations.get(i);
            Player inviter = Bukkit.getPlayer(invitation.getInviterPlayer());
            String inviterName = inviter != null ? inviter.getName() : "Joueur déconnecté";
            
            MessageUtils.sendMessage(player, (i + 1) + ". " + 
                MessageUtils.formatCivilizationName(invitation.getCivilizationName()) + 
                " (par " + MessageUtils.formatPlayerName(inviterName) + 
                ") - Expire dans " + invitation.getRemainingTimeFormatted());
        }
        
        MessageUtils.sendMessage(player, "Utilisez /cv join <civilisation> pour accepter");
        MessageUtils.sendMessage(player, "Utilisez /cv decline <civilisation> pour refuser");
    }
    
    private void startCleanupTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                cleanupExpiredInvitations();
            }
        }.runTaskTimer(plugin, 20 * 60, 20 * 60); // Toutes les minutes
    }
    
    private void cleanupExpiredInvitations() {
        pendingInvitations.values().forEach(invitations -> 
            invitations.removeIf(Invitation::isExpired)
        );
        
        // Nettoyer les listes vides
        pendingInvitations.entrySet().removeIf(entry -> entry.getValue().isEmpty());
    }
    
    public int getTotalPendingInvitations() {
        return pendingInvitations.values().stream()
                .mapToInt(List::size)
                .sum();
    }
    
    public void shutdown() {
        pendingInvitations.clear();
    }
}
