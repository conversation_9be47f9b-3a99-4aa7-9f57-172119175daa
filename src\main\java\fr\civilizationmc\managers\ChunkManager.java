package fr.civilizationmc.managers;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Civilization;
import fr.civilizationmc.models.ClaimedChunk;
import fr.civilizationmc.utils.ConfigUtils;
import org.bukkit.Chunk;
import org.bukkit.entity.Player;

import java.util.UUID;

/**
 * Gestionnaire des chunks revendiqués
 */
public class ChunkManager {
    
    private final CivilizationMc plugin;
    private final DatabaseManager databaseManager;
    private final CivilizationManager civilizationManager;
    
    public ChunkManager(CivilizationMc plugin) {
        this.plugin = plugin;
        this.databaseManager = plugin.getDatabaseManager();
        this.civilizationManager = plugin.getCivilizationManager();
    }

    /**
     * Revendique un chunk pour une civilisation
     */
    public boolean claimChunk(Player player, Chunk chunk) {
        Civilization civilization = civilizationManager.getPlayerCivilization(player.getUniqueId());
        if (civilization == null) {
            return false; // Le joueur n'est pas dans une civilisation
        }
        
        if (!civilization.isOwner(player.getUniqueId())) {
            return false; // Seul le propriétaire peut revendiquer des chunks
        }
        
        String chunkKey = ClaimedChunk.createChunkKey(chunk.getWorld().getName(), chunk.getX(), chunk.getZ());
        
        // Vérifier si le chunk est déjà revendiqué
        if (isChunkClaimed(chunkKey)) {
            return false;
        }
        
        // Vérifier la limite de chunks
        if (civilization.getClaimedChunkCount() >= ConfigUtils.getMaxClaimsPerCivilization()) {
            return false;
        }
        
        // Vérifier si le monde est autorisé
        if (!ConfigUtils.isWorldEnabled(chunk.getWorld().getName()) || 
            ConfigUtils.isWorldDisabled(chunk.getWorld().getName())) {
            return false;
        }
        
        // Créer et sauvegarder le chunk revendiqué
        ClaimedChunk claimedChunk = new ClaimedChunk(
            chunk.getWorld().getName(),
            chunk.getX(),
            chunk.getZ(),
            civilization.getName(),
            player.getUniqueId()
        );
        
        civilization.addClaimedChunk(chunkKey);
        databaseManager.saveCivilization(civilization);
        databaseManager.saveClaimedChunk(claimedChunk);
        
        return true;
    }
    
    /**
     * Abandonne un chunk
     */
    public boolean unclaimChunk(Player player, Chunk chunk) {
        if (!ConfigUtils.isChunkUnclaimAllowed()) {
            return false; // L'abandon de chunks est désactivé
        }
        
        Civilization civilization = civilizationManager.getPlayerCivilization(player.getUniqueId());
        if (civilization == null) {
            return false;
        }
        
        if (!civilization.isOwner(player.getUniqueId())) {
            return false; // Seul le propriétaire peut abandonner des chunks
        }
        
        String chunkKey = ClaimedChunk.createChunkKey(chunk.getWorld().getName(), chunk.getX(), chunk.getZ());
        
        // Vérifier si le chunk est revendiqué par cette civilisation
        ClaimedChunk claimedChunk = getClaimedChunk(chunkKey);
        if (claimedChunk == null || !claimedChunk.getCivilizationName().equals(civilization.getName())) {
            return false;
        }
        
        // Supprimer le chunk
        civilization.removeClaimedChunk(chunkKey);
        databaseManager.saveCivilization(civilization);
        databaseManager.removeClaimedChunk(chunk.getWorld().getName(), chunk.getX(), chunk.getZ());
        
        return true;
    }
    
    /**
     * Vérifie si un chunk est revendiqué
     */
    public boolean isChunkClaimed(String chunkKey) {
        String[] parts = chunkKey.split("_");
        if (parts.length != 3) return false;
        
        String world = parts[0];
        int x = Integer.parseInt(parts[1]);
        int z = Integer.parseInt(parts[2]);
        
        return databaseManager.getClaimedChunk(world, x, z) != null;
    }
    
    /**
     * Vérifie si un chunk est revendiqué
     */
    public boolean isChunkClaimed(Chunk chunk) {
        String chunkKey = ClaimedChunk.createChunkKey(chunk.getWorld().getName(), chunk.getX(), chunk.getZ());
        return isChunkClaimed(chunkKey);
    }
    
    /**
     * Récupère les informations d'un chunk revendiqué
     */
    public ClaimedChunk getClaimedChunk(String chunkKey) {
        String[] parts = chunkKey.split("_");
        if (parts.length != 3) return null;
        
        String world = parts[0];
        int x = Integer.parseInt(parts[1]);
        int z = Integer.parseInt(parts[2]);
        
        return databaseManager.getClaimedChunk(world, x, z);
    }
    
    /**
     * Récupère les informations d'un chunk revendiqué
     */
    public ClaimedChunk getClaimedChunk(Chunk chunk) {
        return databaseManager.getClaimedChunk(chunk.getWorld().getName(), chunk.getX(), chunk.getZ());
    }
    
    /**
     * Vérifie si un joueur peut construire dans un chunk
     */
    public boolean canPlayerBuild(Player player, Chunk chunk) {
        ClaimedChunk claimedChunk = getClaimedChunk(chunk);
        if (claimedChunk == null) {
            return true; // Chunk non revendiqué, construction autorisée
        }
        
        Civilization civilization = civilizationManager.getCivilization(claimedChunk.getCivilizationName());
        if (civilization == null) {
            return true; // Civilisation introuvable, autoriser par défaut
        }
        
        // Vérifier si le joueur est membre de la civilisation
        return civilization.isMember(player.getUniqueId());
    }
    
    /**
     * Vérifie si le PvP est autorisé dans un chunk
     */
    public boolean isPvpAllowed(Chunk chunk) {
        if (!isChunkClaimed(chunk)) {
            return true; // Chunk non revendiqué, PvP autorisé
        }
        
        return ConfigUtils.isPvpAllowedInClaimedChunks();
    }
    
    /**
     * Récupère la civilisation propriétaire d'un chunk
     */
    public Civilization getChunkOwner(Chunk chunk) {
        ClaimedChunk claimedChunk = getClaimedChunk(chunk);
        if (claimedChunk == null) {
            return null;
        }
        
        return civilizationManager.getCivilization(claimedChunk.getCivilizationName());
    }
}
