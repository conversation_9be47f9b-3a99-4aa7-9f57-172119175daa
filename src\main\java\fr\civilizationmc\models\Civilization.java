package fr.civilizationmc.models;

import org.bukkit.Location;
import java.util.*;

public class Civilization {
    
    private String name;
    private UUID ownerId;
    private String ownerName;
    private Location homeLocation;
    private Set<UUID> members;
    private Set<String> claimedChunks;
    private boolean chatEnabled;
    private long creationDate;
    private Map<String, RelationType> relations;
    
    public Civilization(String name, UUID ownerId, String ownerName) {
        this.name = name;
        this.ownerId = ownerId;
        this.ownerName = ownerName;
        this.members = new HashSet<>();
        this.claimedChunks = new HashSet<>();
        this.chatEnabled = false;
        this.creationDate = System.currentTimeMillis();
        this.relations = new HashMap<>();
        
        // Le propriétaire est automatiquement membre
        this.members.add(ownerId);
    }
    
    // Getters
    public String getName() {
        return name;
    }
    
    public UUID getOwnerId() {
        return ownerId;
    }
    
    public String getOwnerName() {
        return ownerName;
    }
    
    public Location getHomeLocation() {
        return homeLocation;
    }
    
    public Set<UUID> getMembers() {
        return new HashSet<>(members);
    }
    
    public Set<String> getClaimedChunks() {
        return new HashSet<>(claimedChunks);
    }
    
    public boolean isChatEnabled() {
        return chatEnabled;
    }
    
    public long getCreationDate() {
        return creationDate;
    }
    
    public Map<String, RelationType> getRelations() {
        return new HashMap<>(relations);
    }
    
    // Setters
    public void setName(String name) {
        this.name = name;
    }
    
    public void setHomeLocation(Location homeLocation) {
        this.homeLocation = homeLocation;
    }
    
    public void setChatEnabled(boolean chatEnabled) {
        this.chatEnabled = chatEnabled;
    }
    
    public void setCreationDate(long creationDate) {
        this.creationDate = creationDate;
    }
    
    // Méthodes utilitaires
    public boolean isMember(UUID playerId) {
        return members.contains(playerId);
    }
    
    public boolean isOwner(UUID playerId) {
        return ownerId.equals(playerId);
    }
    
    public void addMember(UUID playerId) {
        members.add(playerId);
    }
    
    public boolean removeMember(UUID playerId) {
        if (isOwner(playerId)) {
            return false; // Ne peut pas retirer le propriétaire
        }
        return members.remove(playerId);
    }
    
    public void addClaimedChunk(String chunkKey) {
        claimedChunks.add(chunkKey);
    }
    
    public boolean removeClaimedChunk(String chunkKey) {
        return claimedChunks.remove(chunkKey);
    }
    
    public boolean hasClaimedChunk(String chunkKey) {
        return claimedChunks.contains(chunkKey);
    }
    
    public void setRelation(String civilizationName, RelationType relationType) {
        relations.put(civilizationName, relationType);
    }
    
    public RelationType getRelation(String civilizationName) {
        return relations.getOrDefault(civilizationName, RelationType.NEUTRAL);
    }
    
    public int getMemberCount() {
        return members.size();
    }
    
    public int getClaimedChunkCount() {
        return claimedChunks.size();
    }
    
    @Override
    public String toString() {
        return "Civilization{" +
                "name='" + name + '\'' +
                ", owner='" + ownerName + '\'' +
                ", members=" + members.size() +
                ", chunks=" + claimedChunks.size() +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Civilization that = (Civilization) obj;
        return Objects.equals(name, that.name);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}
