package fr.civilizationmc.commands;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Civilization;
import fr.civilizationmc.utils.MessageUtils;
import fr.civilizationmc.utils.ScoreboardUtils;
import org.bukkit.Bukkit;
import org.bukkit.Chunk;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class CivilizationCommand implements CommandExecutor, TabCompleter {
    
    private final CivilizationMc plugin;
    
    public CivilizationCommand(CivilizationMc plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Cette commande ne peut être utilisée que par un joueur!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            // Afficher l'aide par défaut
            sendHelp(player);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "create":
                handleCreate(player, args);
                break;
            case "claim":
                handleClaim(player, args);
                break;
            case "unclaim":
                handleUnclaim(player, args);
                break;
            case "sethome":
                handleSetHome(player, args);
                break;
            case "home":
                handleHome(player, args);
                break;
            case "invite":
                handleInvite(player, args);
                break;
            case "join":
                handleJoin(player, args);
                break;
            case "decline":
                handleDecline(player, args);
                break;
            case "invitations":
                handleInvitations(player, args);
                break;
            case "kick":
                handleKick(player, args);
                break;
            case "chat":
                handleChat(player, args);
                break;
            case "war":
                handleWar(player, args);
                break;
            case "peace":
                handlePeace(player, args);
                break;
            case "ally":
                handleAlly(player, args);
                break;
            case "info":
                handleInfo(player, args);
                break;
            case "list":
                handleList(player, args);
                break;

            case "help":
            default:
                sendHelp(player);
                break;
        }
        
        return true;
    }
    
    private void handleCreate(Player player, String[] args) {
        if (args.length < 2) {
            MessageUtils.sendError(player, "Usage: /cv create <nom>");
            return;
        }
        
        String civilizationName = args[1];
        
        if (plugin.getCivilizationManager().createCivilization(player, civilizationName)) {
            MessageUtils.sendSuccess(player, "Civilisation " + 
                MessageUtils.formatCivilizationName(civilizationName) + " créée avec succès!");
        } else {
            MessageUtils.sendError(player, "Impossible de créer la civilisation. Vérifiez que vous n'êtes pas déjà dans une civilisation et que le nom est valide.");
        }
    }
    
    private void handleClaim(Player player, String[] args) {
        if (plugin.getChunkManager().claimChunk(player, player.getLocation().getChunk())) {
            MessageUtils.sendSuccess(player, "Chunk revendiqué avec succès!");
        } else {
            MessageUtils.sendError(player, "Impossible de revendiquer ce chunk. Vérifiez que vous êtes le propriétaire d'une civilisation et que le chunk n'est pas déjà revendiqué.");
        }
    }
    
    private void handleUnclaim(Player player, String[] args) {
        if (plugin.getChunkManager().unclaimChunk(player, player.getLocation().getChunk())) {
            MessageUtils.sendSuccess(player, "Chunk abandonné avec succès!");
        } else {
            MessageUtils.sendError(player, "Impossible d'abandonner ce chunk.");
        }
    }
    
    private void handleSetHome(Player player, String[] args) {
        if (plugin.getCivilizationManager().setHome(player, player.getLocation())) {
            MessageUtils.sendSuccess(player, "Home défini avec succès!");
        } else {
            MessageUtils.sendError(player, "Impossible de définir le home. Vous devez être le propriétaire d'une civilisation.");
        }
    }
    
    private void handleHome(Player player, String[] args) {
        if (plugin.getCivilizationManager().teleportToHome(player)) {
            MessageUtils.sendSuccess(player, "Téléportation au home...");
        } else {
            MessageUtils.sendError(player, "Impossible de se téléporter au home. Vérifiez qu'un home est défini pour votre civilisation.");
        }
    }
    
    private void handleInvite(Player player, String[] args) {
        if (args.length < 2) {
            MessageUtils.sendError(player, "Usage: /cv invite <joueur>");
            return;
        }
        
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            MessageUtils.sendError(player, "Joueur introuvable.");
            return;
        }
        
        if (target.equals(player)) {
            MessageUtils.sendError(player, "Vous ne pouvez pas vous inviter vous-même!");
            return;
        }
        
        // Vérifier que le joueur est propriétaire d'une civilisation
        Civilization civilization = plugin.getCivilizationManager().getPlayerCivilization(player.getUniqueId());
        if (civilization == null || !civilization.isOwner(player.getUniqueId())) {
            MessageUtils.sendError(player, "Vous devez être propriétaire d'une civilisation pour inviter des joueurs.");
            return;
        }
        
        // Vérifier que le joueur cible n'est pas déjà dans une civilisation
        if (plugin.getCivilizationManager().getPlayerCivilization(target.getUniqueId()) != null) {
            MessageUtils.sendError(player, "Ce joueur fait déjà partie d'une civilisation.");
            return;
        }
        
        // Créer l'invitation
        if (plugin.getInvitationManager().createInvitation(player.getUniqueId(), target.getUniqueId(), civilization.getName())) {
            MessageUtils.sendSuccess(player, "Invitation envoyée à " + MessageUtils.formatPlayerName(target.getName()) + "!");
            MessageUtils.sendInfo(target, "Vous avez été invité à rejoindre la civilisation " + 
                MessageUtils.formatCivilizationName(civilization.getName()) + " par " + 
                MessageUtils.formatPlayerName(player.getName()) + "!");
            MessageUtils.sendInfo(target, "Utilisez /cv join " + civilization.getName() + " pour accepter ou /cv decline " + civilization.getName() + " pour refuser.");
            MessageUtils.sendInfo(target, "L'invitation expire dans 5 minutes. Utilisez /cv invitations pour voir toutes vos invitations.");
        } else {
            MessageUtils.sendError(player, "Ce joueur a déjà une invitation en attente pour votre civilisation.");
        }
    }
    
    private void handleJoin(Player player, String[] args) {
        if (args.length < 2) {
            // Afficher la liste des invitations si aucune civilisation spécifiée
            plugin.getInvitationManager().sendInvitationList(player);
            return;
        }
        
        String civilizationName = args[1];
        
        // Vérifier que le joueur n'est pas déjà dans une civilisation
        if (plugin.getCivilizationManager().getPlayerCivilization(player.getUniqueId()) != null) {
            MessageUtils.sendError(player, "Vous faites déjà partie d'une civilisation.");
            return;
        }
        
        // Accepter l'invitation
        if (plugin.getInvitationManager().acceptInvitation(player.getUniqueId(), civilizationName)) {
            MessageUtils.sendSuccess(player, "Vous avez rejoint la civilisation " + 
                MessageUtils.formatCivilizationName(civilizationName) + "!");
            
            // Notifier les membres de la civilisation
            Civilization civilization = plugin.getCivilizationManager().getCivilization(civilizationName);
            if (civilization != null) {
                MessageUtils.sendCivilizationMessage(civilization.getMembers(), 
                    MessageUtils.formatPlayerName(player.getName()) + " a rejoint la civilisation!");
            }
        } else {
            MessageUtils.sendError(player, "Invitation introuvable ou expirée. Utilisez /cv invitations pour voir vos invitations.");
        }
    }
    
    private void handleDecline(Player player, String[] args) {
        if (args.length < 2) {
            MessageUtils.sendError(player, "Usage: /cv decline <civilisation>");
            return;
        }
        
        String civilizationName = args[1];
        
        if (plugin.getInvitationManager().declineInvitation(player.getUniqueId(), civilizationName)) {
            MessageUtils.sendSuccess(player, "Invitation de " + 
                MessageUtils.formatCivilizationName(civilizationName) + " refusée.");
        } else {
            MessageUtils.sendError(player, "Invitation introuvable.");
        }
    }
    
    private void handleInvitations(Player player, String[] args) {
        plugin.getInvitationManager().sendInvitationList(player);
    }
    
    private void handleKick(Player player, String[] args) {
        if (args.length < 2) {
            MessageUtils.sendError(player, "Usage: /cv kick <joueur>");
            return;
        }
        
        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            MessageUtils.sendError(player, "Joueur introuvable.");
            return;
        }
        
        Civilization civilization = plugin.getCivilizationManager().getPlayerCivilization(player.getUniqueId());
        if (civilization == null) {
            MessageUtils.sendError(player, "Vous n'êtes pas dans une civilisation.");
            return;
        }
        
        if (plugin.getCivilizationManager().removePlayerFromCivilization(target.getUniqueId(), civilization.getName())) {
            MessageUtils.sendSuccess(player, MessageUtils.formatPlayerName(target.getName()) + " a été exclu de la civilisation!");
            MessageUtils.sendWarning(target, "Vous avez été exclu de la civilisation " + 
                MessageUtils.formatCivilizationName(civilization.getName()) + "!");
        } else {
            MessageUtils.sendError(player, "Impossible d'exclure ce joueur.");
        }
    }
    
    private void handleChat(Player player, String[] args) {
        if (plugin.getCivilizationManager().toggleChat(player)) {
            Civilization civilization = plugin.getCivilizationManager().getPlayerCivilization(player.getUniqueId());
            if (civilization.isChatEnabled()) {
                MessageUtils.sendSuccess(player, "Chat privé de civilisation activé!");
            } else {
                MessageUtils.sendSuccess(player, "Chat privé de civilisation désactivé!");
            }
            
            // Mettre à jour le scoreboard en temps réel si le joueur est dans un chunk revendiqué
            Chunk currentChunk = player.getLocation().getChunk();
            String chunkKey = currentChunk.getWorld().getName() + "_" + currentChunk.getX() + "_" + currentChunk.getZ();
            fr.civilizationmc.models.ClaimedChunk claimedChunk = plugin.getChunkManager().getClaimedChunk(chunkKey);
            
            if (claimedChunk != null && ScoreboardUtils.hasActiveScoreboard(player)) {
                ScoreboardUtils.updateScoreboardIfActive(player, claimedChunk);
            }
        } else {
            MessageUtils.sendError(player, "Vous devez être dans une civilisation pour utiliser cette commande.");
        }
    }
    
    private void handleWar(Player player, String[] args) {
        if (args.length < 2) {
            MessageUtils.sendError(player, "Usage: /cv war <civilisation>");
            return;
        }
        
        String targetCivilization = args[1];
        
        if (plugin.getDiplomacyManager().declareWar(player, targetCivilization)) {
            MessageUtils.sendSuccess(player, "Guerre déclarée à " + 
                MessageUtils.formatCivilizationName(targetCivilization) + "!");
        } else {
            MessageUtils.sendError(player, "Impossible de déclarer la guerre à cette civilisation.");
        }
    }
    
    private void handlePeace(Player player, String[] args) {
        if (args.length < 2) {
            MessageUtils.sendError(player, "Usage: /cv peace <civilisation>");
            return;
        }
        
        String targetCivilization = args[1];
        
        if (plugin.getDiplomacyManager().proposePeace(player, targetCivilization)) {
            MessageUtils.sendSuccess(player, "Proposition de paix envoyée à " + 
                MessageUtils.formatCivilizationName(targetCivilization) + "!");
        } else {
            MessageUtils.sendError(player, "Impossible de proposer la paix à cette civilisation.");
        }
    }
    
    private void handleAlly(Player player, String[] args) {
        if (args.length < 2) {
            MessageUtils.sendError(player, "Usage: /cv ally <civilisation>");
            return;
        }
        
        String targetCivilization = args[1];
        
        if (plugin.getDiplomacyManager().proposeAlliance(player, targetCivilization)) {
            MessageUtils.sendSuccess(player, "Proposition d'alliance envoyée à " + 
                MessageUtils.formatCivilizationName(targetCivilization) + "!");
        } else {
            MessageUtils.sendError(player, "Impossible de proposer une alliance à cette civilisation.");
        }
    }
    
    private void handleInfo(Player player, String[] args) {
        Civilization civilization = plugin.getCivilizationManager().getPlayerCivilization(player.getUniqueId());
        if (civilization == null) {
            MessageUtils.sendError(player, "Vous n'êtes pas dans une civilisation.");
            return;
        }
        
        MessageUtils.sendMessage(player, "=== Informations de votre civilisation ===");
        MessageUtils.sendMessage(player, "Nom: " + MessageUtils.formatCivilizationName(civilization.getName()));
        MessageUtils.sendMessage(player, "Propriétaire: " + MessageUtils.formatPlayerName(civilization.getOwnerName()));
        MessageUtils.sendMessage(player, "Membres: " + civilization.getMemberCount());
        MessageUtils.sendMessage(player, "Chunks revendiqués: " + civilization.getClaimedChunkCount());
        MessageUtils.sendMessage(player, "Chat privé: " + (civilization.isChatEnabled() ? "Activé" : "Désactivé"));
    }
    
    private void handleList(Player player, String[] args) {
        MessageUtils.sendMessage(player, "=== Liste des civilisations ===");
        for (Civilization civ : plugin.getDatabaseManager().getAllCivilizations()) {
            MessageUtils.sendMessage(player, "- " + MessageUtils.formatCivilizationName(civ.getName()) + 
                " (" + civ.getMemberCount() + " membres, " + civ.getClaimedChunkCount() + " chunks)");
        }
    }
    


    private void sendHelp(Player player) {
        MessageUtils.sendMessage(player, "=== Commandes CivilizationMc ===");
        MessageUtils.sendMessage(player, "/cv create <nom> - Créer une civilisation");
        MessageUtils.sendMessage(player, "/cv claim - Revendiquer le chunk actuel");
        MessageUtils.sendMessage(player, "/cv unclaim - Abandonner le chunk actuel");
        MessageUtils.sendMessage(player, "/cv sethome - Définir le home de la civilisation");
        MessageUtils.sendMessage(player, "/cv home - Se téléporter au home");
        MessageUtils.sendMessage(player, "/cv invite <joueur> - Inviter un joueur");
        MessageUtils.sendMessage(player, "/cv join [civilisation] - Accepter une invitation");
        MessageUtils.sendMessage(player, "/cv decline <civilisation> - Refuser une invitation");
        MessageUtils.sendMessage(player, "/cv invitations - Voir vos invitations");
        MessageUtils.sendMessage(player, "/cv kick <joueur> - Exclure un joueur");
        MessageUtils.sendMessage(player, "/cv chat - Activer/désactiver le chat privé");
        MessageUtils.sendMessage(player, "/cv war <civilisation> - Déclarer la guerre");
        MessageUtils.sendMessage(player, "/cv peace <civilisation> - Proposer la paix");
        MessageUtils.sendMessage(player, "/cv ally <civilisation> - Proposer une alliance");
        MessageUtils.sendMessage(player, "/cv info - Informations de votre civilisation");
        MessageUtils.sendMessage(player, "/cv list - Liste des civilisations");

    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            List<String> subCommands = Arrays.asList(
                "create", "claim", "unclaim", "sethome", "home",
                "invite", "join", "decline", "invitations", "kick", "chat", "war", "peace",
                "ally", "info", "list", "help"
            );
            
            for (String subCommand : subCommands) {
                if (subCommand.toLowerCase().startsWith(args[0].toLowerCase())) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2) {
            String subCommand = args[0].toLowerCase();
            
            if (subCommand.equals("invite") || subCommand.equals("kick")) {
                // Complétion des noms de joueurs
                for (Player player : Bukkit.getOnlinePlayers()) {
                    if (player.getName().toLowerCase().startsWith(args[1].toLowerCase())) {
                        completions.add(player.getName());
                    }
                }
            } else if (subCommand.equals("war") || subCommand.equals("peace") || subCommand.equals("ally")) {
                // Complétion des noms de civilisations
                for (Civilization civ : plugin.getDatabaseManager().getAllCivilizations()) {
                    if (civ.getName().toLowerCase().startsWith(args[1].toLowerCase())) {
                        completions.add(civ.getName());
                    }
                }
            } else if (subCommand.equals("join") || subCommand.equals("decline")) {
                // Complétion des civilisations pour lesquelles le joueur a des invitations
                if (sender instanceof Player) {
                    Player player = (Player) sender;
                    plugin.getInvitationManager().getPlayerInvitations(player.getUniqueId())
                            .forEach(invitation -> {
                                if (invitation.getCivilizationName().toLowerCase().startsWith(args[1].toLowerCase())) {
                                    completions.add(invitation.getCivilizationName());
                                }
                            });
                }
            }
        }
        
        return completions;
    }
}
