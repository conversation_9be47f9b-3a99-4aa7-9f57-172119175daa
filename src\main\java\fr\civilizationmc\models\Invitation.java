package fr.civilizationmc.models;

import java.util.UUID;

public class Invitation {
    
    private final UUID invitedPlayer;
    private final String civilizationName;
    private final UUID inviterPlayer;
    private final long timestamp;
    private final long expirationTime;
    
    public Invitation(UUID invitedPlayer, String civilizationName, UUID inviterPlayer) {
        this.invitedPlayer = invitedPlayer;
        this.civilizationName = civilizationName;
        this.inviterPlayer = inviterPlayer;
        this.timestamp = System.currentTimeMillis();
        this.expirationTime = timestamp + (5 * 60 * 1000); // 5 minutes d'expiration
    }
    
    public UUID getInvitedPlayer() {
        return invitedPlayer;
    }
    
    public String getCivilizationName() {
        return civilizationName;
    }
    
    public UUID getInviterPlayer() {
        return inviterPlayer;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public long getExpirationTime() {
        return expirationTime;
    }
    
    public boolean isExpired() {
        return System.currentTimeMillis() > expirationTime;
    }
    
    public long getRemainingTime() {
        long remaining = expirationTime - System.currentTimeMillis();
        return Math.max(0, remaining);
    }
    
    public String getRemainingTimeFormatted() {
        long remaining = getRemainingTime();
        if (remaining <= 0) {
            return "Expirée";
        }
        
        long seconds = remaining / 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;
        
        if (minutes > 0) {
            return minutes + "m " + seconds + "s";
        } else {
            return seconds + "s";
        }
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        Invitation that = (Invitation) obj;
        return invitedPlayer.equals(that.invitedPlayer) && 
               civilizationName.equals(that.civilizationName);
    }
    
    @Override
    public int hashCode() {
        return invitedPlayer.hashCode() + civilizationName.hashCode();
    }
    
    @Override
    public String toString() {
        return "Invitation{" +
                "invitedPlayer=" + invitedPlayer +
                ", civilizationName='" + civilizationName + '\'' +
                ", inviterPlayer=" + inviterPlayer +
                ", remainingTime=" + getRemainingTimeFormatted() +
                '}';
    }
}
