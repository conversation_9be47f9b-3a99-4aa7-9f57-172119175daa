package fr.civilizationmc.listeners;

import fr.civilizationmc.CivilizationMc;
import fr.civilizationmc.models.Civilization;
import fr.civilizationmc.models.ClaimedChunk;
import fr.civilizationmc.utils.ConfigUtils;
import fr.civilizationmc.utils.MessageUtils;
import fr.civilizationmc.utils.ScoreboardUtils;
import org.bukkit.Chunk;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.player.PlayerQuitEvent;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class PlayerListener implements Listener {
    
    private final CivilizationMc plugin;
    
    // Map pour stocker le dernier chunk de chaque joueur
    private final Map<UUID, String> lastPlayerChunk = new ConcurrentHashMap<>();
    
    public PlayerListener(CivilizationMc plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // Vérifier si le joueur a une civilisation
        Civilization civilization = plugin.getCivilizationManager().getPlayerCivilization(player.getUniqueId());
        if (civilization != null) {
            MessageUtils.sendInfo(player, "Bienvenue dans votre civilisation " + 
                MessageUtils.formatCivilizationName(civilization.getName()) + "!");
        }
        
        // Vérifier les invitations en attente
        if (plugin.getInvitationManager().hasAnyInvitation(player.getUniqueId())) {
            MessageUtils.sendInfo(player, "Vous avez des invitations en attente! Utilisez /cv invitations pour les voir.");
        }
        
        // Initialiser le chunk du joueur pour le système de scoreboard
        Chunk currentChunk = player.getLocation().getChunk();
        String chunkKey = currentChunk.getWorld().getName() + "_" + currentChunk.getX() + "_" + currentChunk.getZ();
        lastPlayerChunk.put(player.getUniqueId(), chunkKey);
        
        // Vérifier si le joueur spawn dans un chunk revendiqué
        ClaimedChunk claimedChunk = plugin.getChunkManager().getClaimedChunk(chunkKey);
        if (claimedChunk != null) {
            ScoreboardUtils.showCivilizationScoreboard(player, claimedChunk);
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // Nettoyer les scoreboards
        ScoreboardUtils.cleanupPlayerScoreboards(player);
        
        // Nettoyer la map des chunks
        lastPlayerChunk.remove(player.getUniqueId());
    }
    
    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        // Vérifier si le système de scoreboard est activé
        if (!ConfigUtils.isScoreboardEnabled()) {
            return;
        }
        
        Player player = event.getPlayer();
        
        // Vérifier si le joueur a changé de chunk
        Chunk fromChunk = event.getFrom().getChunk();
        Chunk toChunk = event.getTo().getChunk();
        
        // Si le joueur n'a pas changé de chunk, ignorer
        if (fromChunk.getX() == toChunk.getX() && fromChunk.getZ() == toChunk.getZ() && 
            fromChunk.getWorld().equals(toChunk.getWorld())) {
            return;
        }
        
        UUID playerId = player.getUniqueId();
        String newChunkKey = toChunk.getWorld().getName() + "_" + toChunk.getX() + "_" + toChunk.getZ();
        String lastChunkKey = lastPlayerChunk.get(playerId);
        
        // Si c'est le même chunk que le dernier enregistré, ignorer
        if (newChunkKey.equals(lastChunkKey)) {
            return;
        }
        
        // Mettre à jour le dernier chunk
        lastPlayerChunk.put(playerId, newChunkKey);
        
        // Vérifier si l'ancien chunk était revendiqué
        boolean wasInClaimedChunk = false;
        if (lastChunkKey != null) {
            ClaimedChunk lastClaimedChunk = plugin.getChunkManager().getClaimedChunk(lastChunkKey);
            wasInClaimedChunk = (lastClaimedChunk != null);
        }
        
        // Vérifier si le nouveau chunk est revendiqué
        ClaimedChunk newClaimedChunk = plugin.getChunkManager().getClaimedChunk(newChunkKey);
        boolean isInClaimedChunk = (newClaimedChunk != null);
        
        // Gérer l'affichage/masquage du scoreboard
        if (wasInClaimedChunk && !isInClaimedChunk) {
            // Sortie d'un chunk revendiqué vers un chunk libre
            ScoreboardUtils.hideCivilizationScoreboard(player);
        } else if (!wasInClaimedChunk && isInClaimedChunk) {
            // Entrée dans un chunk revendiqué depuis un chunk libre
            ScoreboardUtils.showCivilizationScoreboard(player, newClaimedChunk);
        } else if (wasInClaimedChunk && isInClaimedChunk) {
            // Passage d'un chunk revendiqué à un autre chunk revendiqué
            ScoreboardUtils.showCivilizationScoreboard(player, newClaimedChunk);
        }
        // Si (!wasInClaimedChunk && !isInClaimedChunk) -> pas de changement nécessaire
    }
    
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        
        // Vérifier si le joueur est dans une civilisation avec chat activé
        Civilization civilization = plugin.getCivilizationManager().getPlayerCivilization(player.getUniqueId());
        if (civilization != null && civilization.isChatEnabled()) {
            // Annuler le chat public et envoyer le message au chat privé de la civilisation
            event.setCancelled(true);
            
            MessageUtils.sendCivilizationChat(
                civilization.getMembers(),
                civilization.getName(),
                player.getName(),
                event.getMessage()
            );
        }
    }
}
