---
trigger: always_on
---

Règle à respecter pour toute modification du plugin Minecraft CivilizationMc:

Toujours respecter le cahier des charges.
Avant toute modification, relis attentivement le cahier des charges pour bien comprendre les objectifs et les contraintes du plugin.

Ne jamais casser le code existant.
Chaque modification doit préserver l’intégrité et le bon fonctionnement des fonctionnalités déjà en place. Aucune régression ne doit être introduite.

Analyser l’ensemble du code avant de modifier.
Avant d’ajouter, modifier ou supprimer du code, analyse l’architecture globale du plugin plus tout le code, pour comprendre les dépendances, l’impact potentiel des changements, et garantir une intégration propre.

Le plugin aura une belle structure et professionnel.

voici le chahier des charge :
Le plugin permet de  créer ta propre civilisation avec /cv create tu peux ensuite revendiquer des chunks avec /cv claim définir un home avec /cv sethome et t’y téléporter avec /cv home tu gères les membres avec /cv invite /cv join /cv kick et tu peux même activer un chat privé avec /cv chat côté diplomatie tu peux déclarer une guerre avec /cv war proposer une paix avec /cv peace ou une alliance avec /cv ally